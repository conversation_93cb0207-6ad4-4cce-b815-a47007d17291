// ffrCalculations.js - Implementation of FFR calculations for tunnel wall coloring

/**
 * Checks if SIMD is available in the current browser
 * @returns {boolean} True if SIMD is available
 */
function isSIMDAvailable() {
    return typeof WebAssembly !== 'undefined' &&
           typeof WebAssembly.validate === 'function' &&
           WebAssembly.validate(new Uint8Array([
               0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11
           ]));
}

/**
 * Vectorized version of coefficient calculation using SIMD if available
 * This is a placeholder for future implementation when SIMD.js becomes more widely available
 * @param {Float32Array} diameters - Array of vessel diameters
 * @param {Float32Array} lengths - Array of vessel segment lengths
 * @returns {Object} Object containing viscous (F) and separation (S) loss coefficients
 */
function vectorizedCalculation(diameters, lengths) {
    // Check if SIMD is available
    if (isSIMDAvailable()) {
        console.log("SIMD is available, but not yet implemented in this version");
        // Future implementation will go here when SIMD.js is more widely available
    }

    // Fall back to standard calculation
    return calculateCoefficients(diameters, lengths);
}


/**
 * Calculates pressure loss coefficients for each vessel segment
 * @param {Float32Array|Array} diameters - Array of vessel diameters
 * @param {Float32Array|Array} lengths - Array of vessel segment lengths
 * @param {number} mu - Blood viscosity (default: 0.0035 Pa·s)
 * @param {number} rho - Blood density (default: 1060 kg/m³)
 * @returns {Object} Object containing viscous (F) and separation (S) loss coefficients
 */
const calculateCoefficients = (diameters, lengths, mu = 0.0035, rho = 1060) => {
    // Ensure we're working with typed arrays for better performance
    const diamArray = diameters instanceof Float32Array ? diameters : new Float32Array(diameters);
    const lenArray = lengths instanceof Float32Array ? lengths : new Float32Array(lengths);

    const F = new Float32Array(diamArray.length);
    const S = new Float32Array(diamArray.length);

    // Pre-calculate constants
    const piVal = Math.PI;
    const piInv = 1 / piVal;
    const mu128 = 128 * mu;
    const rho05 = 0.5 * rho;
    const piQuarter = 0.25 * piVal;

    // Use regular for loop for best performance
    for (let i = 0; i < diamArray.length; i++) {
        const D = diamArray[i];
        const L = lenArray[i];
        const D_ref = diamArray[Math.max(0, i - 1)];

        // Use multiplication instead of Math.pow for better performance
        const D2 = D * D;
        const D4 = D2 * D2;

        const A = piQuarter * D2;
        const A_ref = piQuarter * D_ref * D_ref;

        // Calculate coefficients
        F[i] = (mu128 * L) * piInv / D4;

        // Calculate separation loss
        const invA = 1 / A;
        const invA_ref = 1 / A_ref;
        const diffInv = invA - invA_ref;
        S[i] = rho05 * diffInv * diffInv;
    }

    return {F, S};
};

/**
 * Computes FFR value based on pressure loss coefficients
 * @param {Float32Array|Array} F - Viscous loss coefficients
 * @param {Float32Array|Array} S - Separation loss coefficients
 * @param {number} Pao - Aortic pressure (default: 100 mmHg)
 * @param {number} Q - Flow rate (default: 4e-6 m³/s)
 * @returns {number} FFR value (ratio of distal to proximal pressure)
 */
function computeFFR(F, S, Pao = 100, Q = 4e-6) {
    // Pre-calculate Q^2 for better performance
    const Q2 = Q * Q;

    // Use a simple for loop instead of reduce for better performance
    let totalDeltaP = 0;
    for (let i = 0; i < F.length; i++) {
        totalDeltaP += F[i] * Q + S[i] * Q2;
    }

    // Calculate and return FFR value
    return (Pao - totalDeltaP) / Pao;
}

/**
 * Memoized version of calculateCoefficients to improve performance
 */
const memoizedCoefficients = (() => {
    const cache = new Map();
    const MAX_CACHE_SIZE = 50; // Limit cache size to prevent memory leaks

    return (diameters, lengths) => {
        // Create a more efficient cache key using hash of values
        // This is faster than joining arrays and produces shorter keys
        let key = '';
        for (let i = 0; i < diameters.length; i++) {
            // Use only 4 significant digits to improve cache hits
            key += (Math.round(diameters[i] * 10000) / 10000).toFixed(4);
        }
        key += '_';
        for (let i = 0; i < lengths.length; i++) {
            key += (Math.round(lengths[i] * 10000) / 10000).toFixed(4);
        }

        // Check if result is already cached
        if (!cache.has(key)) {
            // Limit cache size
            if (cache.size >= MAX_CACHE_SIZE) {
                // Remove oldest entry (first key in map)
                const firstKey = cache.keys().next().value;
                cache.delete(firstKey);
            }

            // Calculate and cache result
            cache.set(key, calculateCoefficients(diameters, lengths));
        }

        return cache.get(key);
    };
})();

/**
 * Calculates FFR values for tunnel sections and returns color mapping
 * @returns {Array} Array of colors for each tunnel section
 */
function calculateTunnelFFR() {
    // Performance measurement
    const startTime = performance.now();

    const tunnelSections = getTunnelSections();
    if (!tunnelSections || tunnelSections.length === 0) {
        console.warn("No tunnel sections available for FFR calculation");
        return [];
    }

    // Extract diameters and lengths from tunnel sections
    const sectionCount = tunnelSections.length;
    const diameters = new Float32Array(sectionCount);
    const lengths = new Float32Array(sectionCount);

    // Find the average radius to use as a reference for narrowing detection
    let totalRadius = 0;
    for (let i = 0; i < sectionCount; i++) {
        totalRadius += tunnelSections[i].radius;
    }
    const avgRadius = totalRadius / sectionCount;

    // Use a single loop to extract data for better performance
    for (let i = 0; i < sectionCount; i++) {
        const section = tunnelSections[i];
        // Convert radius to diameter (multiply by 2) and convert to meters
        diameters[i] = section.radius * 2 / 1000;
        lengths[i] = section.length / 1000;

        // Mark sections as narrowed if their radius is significantly smaller than average
        // This information will be used for visualization
        section.isNarrowed = section.radius < (avgRadius * 0.7); // 70% of average radius is considered narrowed
    }

    // Calculate coefficients using memoized function
    const {F, S} = memoizedCoefficients(diameters, lengths);

    // Pre-allocate arrays for better performance
    const ffrValues = new Float32Array(sectionCount);
    const cumulativeF = new Float32Array(sectionCount);
    const cumulativeS = new Float32Array(sectionCount);

    // Constants for FFR calculation
    const Pao = 100; // Aortic pressure (mmHg)
    const Q = 4e-6;  // Flow rate (m³/s)
    const Q2 = Q * Q; // Pre-calculate Q^2 for better performance

    // Calculate first section
    cumulativeF[0] = F[0];
    cumulativeS[0] = S[0];
    const totalDeltaP0 = cumulativeF[0] * Q + cumulativeS[0] * Q2;
    ffrValues[0] = (Pao - totalDeltaP0) / Pao;

    // Calculate remaining sections with cumulative values
    for (let i = 1; i < sectionCount; i++) {
        cumulativeF[i] = cumulativeF[i-1] + F[i];
        cumulativeS[i] = cumulativeS[i-1] + S[i];

        // Calculate FFR at this point using cumulative pressure loss
        const totalDeltaP = cumulativeF[i] * Q + cumulativeS[i] * Q2;
        ffrValues[i] = (Pao - totalDeltaP) / Pao;

        // Adjust FFR values based on narrowing for better visualization
        // This ensures narrowed areas are clearly visible with appropriate coloring
        if (tunnelSections[i].isNarrowed) {
            // Reduce FFR value for narrowed sections to ensure they appear in deep red to red range
            // Using 0.45 to place it in the critical stenosis range (0.00-0.50)
            ffrValues[i] = Math.min(ffrValues[i], 0.45);
        } else if (tunnelSections[i].radius > (avgRadius * 1.3)) {
            // Increase FFR value for wider sections to ensure they appear in cyan to blue range
            // Using 0.96 to place it in the excellent flow range (0.95-1.00)
            ffrValues[i] = Math.max(ffrValues[i], 0.96);
        }
    }

    // Map FFR values to colors (use regular array for BABYLON.Color3 objects)
    const colorArray = new Array(sectionCount);
    for (let i = 0; i < sectionCount; i++) {
        colorArray[i] = getColorForFFR(ffrValues[i]);
    }

    // Store FFR values and colors in global variables for later use
    if (typeof tunnelFFRValues !== 'undefined') {
        tunnelFFRValues = Array.from(ffrValues);
    }

    if (typeof tunnelFFRColors !== 'undefined') {
        tunnelFFRColors = colorArray.slice();
    }

    // Log performance metrics in debug mode
    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
        const endTime = performance.now();
        console.log(`FFR calculation completed in ${(endTime - startTime).toFixed(2)}ms for ${sectionCount} sections`);
    }

    return colorArray;
}

/**
 * Maps FFR value to a color
 * @param {number} ffr - FFR value (0-1)
 * @returns {BABYLON.Color3} Color representing the FFR value
 */
function getColorForFFR(ffr) {
    // Ensure FFR is within valid range (use Math.min/max for performance)
    const clampedFFR = Math.max(0, Math.min(1, ffr));

    // Create a more diverse and smooth color gradient based on FFR value
    // Enhanced FFR color scale with more ranges for better differentiation:
    // 0.00-0.50: Deep Red to Red (critical stenosis)
    // 0.50-0.60: Red to Orange-Red (severe stenosis)
    // 0.60-0.70: Orange-Red to Orange (moderate stenosis)
    // 0.70-0.75: Orange to Yellow-Orange (mild stenosis)
    // 0.75-0.80: Yellow-Orange to Yellow (borderline)
    // 0.80-0.85: Yellow to Yellow-Green (normal lower)
    // 0.85-0.90: Yellow-Green to Green (normal)
    // 0.90-0.95: Green to Cyan (good flow)
    // 0.95-1.00: Cyan to Blue (excellent flow)

    // Use memoization for common FFR values to improve performance
    const memoKey = Math.round(clampedFFR * 100);
    if (getColorForFFR.cache && getColorForFFR.cache[memoKey]) {
        return getColorForFFR.cache[memoKey];
    }

    // Calculate color based on FFR value
    let color;

    // Enhance color intensity to make FFR coloring visible but not overwhelming
    // Balance between visibility and not washing out textures
    const intensityFactor = 1.2; // Reduced from 1.8 to 1.2 for better color accuracy

    if (clampedFFR <= 0.50) {
        // Deep Red to Red gradient for critical stenosis
        const t = clampedFFR / 0.50; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, (0.8 + t * 0.2) * intensityFactor), // Red increases from deep red to bright red
            0, // No green
            0 // No blue for pure red color
        );
    } else if (clampedFFR <= 0.60) {
        // Red to Orange-Red gradient for severe stenosis
        const t = (clampedFFR - 0.50) / 0.10; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, 1 * intensityFactor), // Red stays at max
            Math.min(1, t * 0.3 * intensityFactor), // Green increases slightly for orange-red
            0 // No blue
        );
    } else if (clampedFFR <= 0.70) {
        // Orange-Red to Orange gradient for moderate stenosis
        const t = (clampedFFR - 0.60) / 0.10; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, 1 * intensityFactor), // Red stays at max
            Math.min(1, (0.3 + t * 0.3) * intensityFactor), // Green increases more for orange
            0 // No blue
        );
    } else if (clampedFFR <= 0.75) {
        // Orange to Yellow-Orange gradient for mild stenosis
        const t = (clampedFFR - 0.70) / 0.05; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, 1 * intensityFactor), // Red stays at max
            Math.min(1, (0.6 + t * 0.2) * intensityFactor), // Green increases for yellow-orange
            0 // No blue
        );
    } else if (clampedFFR <= 0.80) {
        // Yellow-Orange to Yellow gradient for borderline
        const t = (clampedFFR - 0.75) / 0.05; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, 1 * intensityFactor), // Red stays at max
            Math.min(1, (0.8 + t * 0.2) * intensityFactor), // Green increases to max for yellow
            0 // No blue
        );
    } else if (clampedFFR <= 0.85) {
        // Yellow to Yellow-Green gradient for normal lower
        const t = (clampedFFR - 0.80) / 0.05; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, (1 - t * 0.5) * intensityFactor), // Red decreases
            Math.min(1, 1 * intensityFactor), // Green stays at max
            0 // No blue
        );
    } else if (clampedFFR <= 0.90) {
        // Yellow-Green to Green gradient for normal
        const t = (clampedFFR - 0.85) / 0.05; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            Math.min(1, (0.5 - t * 0.5) * intensityFactor), // Red continues to decrease
            Math.min(1, 1 * intensityFactor), // Green stays at max
            0 // No blue
        );
    } else if (clampedFFR <= 0.95) {
        // Green to Cyan gradient for good flow
        const t = (clampedFFR - 0.90) / 0.05; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            0, // No red
            Math.min(1, 1 * intensityFactor), // Green stays at max
            Math.min(1, t * intensityFactor) // Blue increases for cyan
        );
    } else {
        // Cyan to Blue gradient for excellent flow
        const t = (clampedFFR - 0.95) / 0.05; // Normalize to 0-1 range
        color = new BABYLON.Color3(
            0, // No red
            Math.min(1, (1 - t * 0.7) * intensityFactor), // Green decreases
            Math.min(1, 1 * intensityFactor) // Blue stays at max
        );
    }

    // Initialize cache if it doesn't exist
    if (!getColorForFFR.cache) {
        getColorForFFR.cache = {};
    }

    // Cache the result (limit cache size to 101 entries - one for each percentage point)
    if (Object.keys(getColorForFFR.cache).length < 101) {
        getColorForFFR.cache[memoKey] = color;
    }

    return color;
}

/**
 * Gets the tunnel mesh from the global scope
 * @returns {BABYLON.Mesh|null} The tunnel mesh or null if not found
 */
function getTunnelMesh() {
    // Próbujemy pobrać mesh tunelu z globalnej zmiennej tunnelMesh
    if (typeof tunnelMesh !== 'undefined' && tunnelMesh) {
        return tunnelMesh;
    }

    // Jeśli nie znaleziono, spróbuj pobrać z window.tunnelMesh
    if (typeof window !== 'undefined' && window.tunnelMesh) {
        return window.tunnelMesh;
    }

    // Jeśli nadal nie znaleziono, spróbuj znaleźć mesh w scenie
    if (typeof scene !== 'undefined' && scene) {
        const mesh = scene.getMeshByName("tunnelMesh");
        if (mesh) {
            return mesh;
        }
    }

    console.warn("Could not find tunnel mesh for FFR coloring");
    return null;
}

// The main applyFFRColoring function is defined below

/**
 * Gets the tunnel sections from the global scope
 * @returns {Array|null} The tunnel sections or null if not found
 */
function getTunnelSections() {
    // Próbujemy pobrać sekcje tunelu z globalnej zmiennej tunnelSections
    if (typeof tunnelSections !== 'undefined' && tunnelSections && tunnelSections.length > 0) {
        return tunnelSections;
    }

    // Jeśli nie znaleziono, spróbuj pobrać z window.tunnelSections
    if (typeof window !== 'undefined' && window.tunnelSections && window.tunnelSections.length > 0) {
        return window.tunnelSections;
    }

    console.warn("Could not find tunnel sections for FFR calculations");
    return null;
}

/**
 * Simple performance measurement utility
 * @param {Function} fn - Function to measure
 * @param {Array} args - Arguments to pass to the function
 * @returns {Object} Object containing the result and execution time
 */
function measurePerformance(fn, args = []) {
    const startTime = performance.now();
    const result = fn(...args);
    const endTime = performance.now();
    const executionTime = endTime - startTime;

    return { result, executionTime };
}

/**
 * Applies FFR-based coloring to the tunnel mesh
 */
function applyFFRColoring() {
    // Start performance measurement
    const startTime = performance.now();

    const tunnelMesh = getTunnelMesh();
    if (!tunnelMesh) {
        console.warn("No tunnel mesh available for FFR coloring");
        return;
    }

    const tunnelSections = getTunnelSections();
    if (!tunnelSections || tunnelSections.length === 0) {
        console.warn("No tunnel sections available for FFR coloring");
        return;
    }

    // Check if we already have pre-calculated FFR colors
    let ffrColors;
    let executionTime = 0;

    if (typeof tunnelFFRColors !== 'undefined' && tunnelFFRColors && tunnelFFRColors.length > 0) {
        // Use pre-calculated FFR colors
        ffrColors = tunnelFFRColors;
        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
            console.log(`Using pre-calculated FFR colors for ${ffrColors.length} sections`);
        }
    } else {
        // Calculate FFR colors with performance measurement
        const result = measurePerformance(calculateTunnelFFR);
        ffrColors = result.result;
        executionTime = result.executionTime;

        if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
            console.log(`FFR color calculation completed in ${executionTime.toFixed(2)}ms`);
        }
    }

    if (!ffrColors || ffrColors.length === 0) {
        console.warn("No FFR colors calculated");
        return;
    }

    try {
        // Clear existing submeshes to prevent issues
        tunnelMesh.subMeshes = [];

        // Create a multi-material for the tunnel with FFR-based colors
        const multiMaterial = new BABYLON.MultiMaterial("tunnelMultiMaterial", tunnelMesh.getScene());

        // Create materials for each section with FFR-based colors
        for (let i = 0; i < tunnelSections.length; i++) {
            const sectionMaterial = new BABYLON.StandardMaterial(`tunnelSectionMaterial_${i}`, tunnelMesh.getScene());

            // Get the FFR color for this section
            const ffrColor = ffrColors[Math.min(i, ffrColors.length - 1)];

            // Set up material properties
            sectionMaterial.specularColor = BABYLON.Color3.Black();
            sectionMaterial.backFaceCulling = false;

            // Apply texture if available
            try {
                const scene = tunnelMesh.getScene();
                if (scene) {
                    // Ensure texture is loaded and applied
                    const texture = new BABYLON.Texture("./textures/others_0003_color_1k.jpg", scene, false, false, BABYLON.Texture.BILINEAR_SAMPLINGMODE);
                    if (texture) {
                        // Apply texture
                        sectionMaterial.diffuseTexture = texture;
                        sectionMaterial.diffuseTexture.uScale = 5;
                        sectionMaterial.diffuseTexture.vScale = 30;
                        sectionMaterial.diffuseTexture.hasAlpha = false;

                        // Add normal map for better detail (using OpenGL format)
                        try {
                            const normalTexture = new BABYLON.Texture("./textures/others_0003_normal_opengl_1k.png", scene);
                            sectionMaterial.normalTexture = normalTexture;
                            if (DEBUG_MODE) console.log("Applied OpenGL normal map to tunnel section material");
                        } catch (normalError) {
                            console.warn("Could not apply normal map to tunnel section:", normalError);
                        }

                        // Add height map for bump mapping
                        try {
                            const heightTexture = new BABYLON.Texture("./textures/others_0003_height_1k.png", scene);
                            sectionMaterial.bumpTexture = heightTexture;
                            sectionMaterial.bumpTexture.level = 0.8;
                            if (DEBUG_MODE) console.log("Applied height map for bump mapping to tunnel section");
                        } catch (heightError) {
                            console.warn("Could not apply height map to tunnel section:", heightError);
                        }

                        // Keep texture as primary visual element
                        sectionMaterial.diffuseTexture.level = 1.0; // Full texture visibility

                        // Apply FFR color as a very subtle tint to preserve texture appearance
                        // Mix texture color with FFR color using a much lighter blend
                        const subtleTint = ffrColor.scale(0.3); // Much more subtle tint
                        const neutralBase = new BABYLON.Color3(0.9, 0.9, 0.9); // Light neutral base
                        sectionMaterial.diffuseColor = neutralBase.add(subtleTint);

                        // Use very subtle emissive color only for critical areas (red)
                        if (ffrColor.r > 0.8 && ffrColor.g < 0.2 && ffrColor.b < 0.2) {
                            // Only add subtle red glow for critical stenosis
                            sectionMaterial.emissiveColor = new BABYLON.Color3(0.2, 0.05, 0.05);
                        } else {
                            // No emissive color for other areas to preserve natural look
                            sectionMaterial.emissiveColor = new BABYLON.Color3(0, 0, 0);
                        }

                        // Very subtle ambient color that doesn't interfere with textures
                        sectionMaterial.ambientColor = ffrColor.scale(0.1);

                        // Add point light in this tunnel segment to enhance FFR coloring
                        try {
                            const section = tunnelSections[i];
                            const scene = tunnelMesh.getScene();
                            if (scene && section.position) {
                                // Create a point light at the center of the tunnel segment
                                const light = new BABYLON.PointLight(
                                    `tunnelLight_${i}`,
                                    section.position.clone(),
                                    scene
                                );

                                // Set light properties based on FFR color
                                // Use very subtle light color to avoid overwhelming the texture
                                light.diffuse = ffrColor.scale(0.3); // Much more subtle lighting
                                light.specular = BABYLON.Color3.Black(); // No specular to avoid shiny spots

                                // Set light intensity based on FFR value (lower FFR = brighter light to highlight issues)
                                // Calculate approximate FFR value from color components using improved formula for new color scheme
                                // This formula better handles the full range of our diverse color scheme
                                let ffrApprox;
                                if (ffrColor.b > 0.5 && ffrColor.g < 0.5) {
                                    // Cyan to Blue range (FFR > 0.95)
                                    ffrApprox = Math.min(1.0, 0.95 + (ffrColor.b - ffrColor.g) * 0.1);
                                } else if (ffrColor.g > 0.7 && ffrColor.r < 0.3) {
                                    // Green to Cyan range (0.90 < FFR ≤ 0.95)
                                    ffrApprox = Math.min(0.95, 0.90 + ffrColor.b * 0.1);
                                } else if (ffrColor.g > 0.7) {
                                    // Yellow-Green to Green range (0.80 < FFR ≤ 0.90)
                                    ffrApprox = Math.min(0.90, 0.80 + (1.0 - ffrColor.r) * 0.1);
                                } else if (ffrColor.r > 0.7 && ffrColor.g > 0.7) {
                                    // Yellow range (0.75 < FFR ≤ 0.80)
                                    ffrApprox = Math.min(0.80, 0.75 + ffrColor.g * 0.05);
                                } else if (ffrColor.r > 0.7 && ffrColor.g > 0.3) {
                                    // Orange to Yellow-Orange range (0.60 < FFR ≤ 0.75)
                                    ffrApprox = Math.min(0.75, 0.60 + ffrColor.g * 0.15);
                                } else if (ffrColor.r > 0.7 && ffrColor.b < 0.1) {
                                    // Red to Orange-Red range (0.50 < FFR ≤ 0.60)
                                    ffrApprox = Math.min(0.60, 0.50 + ffrColor.g * 0.33);
                                } else {
                                    // For very low FFR values, we approximate FFR using red/blue channel
                                    // Deep Red to Red range (FFR ≤ 0.50)
                                    ffrApprox = Math.max(0.3, 0.50 * (1.0 - ffrColor.b));
                                    console.log(`[FFR Light] Section`, i, 'ffrColor:', ffrColor, 'ffrApprox:', ffrApprox);
                                }

                                // Use very subtle intensity to avoid overwhelming textures
                                let intensity = 0.2 * (1.0 - ffrApprox) + 0.1; // Much more subtle lighting

                                // Only slightly boost intensity for critical areas (red)
                                if (ffrColor.r > 0.8 && ffrColor.g < 0.2 && ffrColor.b < 0.2) {
                                    intensity *= 1.2; // Only 20% brighter for critical areas
                                }

                                light.intensity = intensity;

                                // Use smaller range to keep lighting very localized
                                light.range = section.length * 0.8; // Even more localized lighting

                                // Add metadata to track these lights for cleanup
                                light.metadata = { type: "ffrLight", segmentIndex: i };
                            }
                        } catch (lightError) {
                            console.warn(`Could not create light for tunnel section ${i}:`, lightError);
                        }

                        // Add very subtle fresnel effect only for critical areas
                        if (ffrColor.r > 0.8 && ffrColor.g < 0.2 && ffrColor.b < 0.2) {
                            sectionMaterial.emissiveFresnelParameters = new BABYLON.FresnelParameters();
                            sectionMaterial.emissiveFresnelParameters.bias = 0.1; // Very subtle
                            sectionMaterial.emissiveFresnelParameters.power = 2.0; // Sharp falloff
                            sectionMaterial.emissiveFresnelParameters.leftColor = BABYLON.Color3.White();
                            sectionMaterial.emissiveFresnelParameters.rightColor = new BABYLON.Color3(0.3, 0.1, 0.1); // Very subtle red edge
                        }

                        // Add very subtle glow layer only for critical FFR values (only deep red areas)
                        try {
                            // Only add glow to sections with very low FFR (critical stenosis only)
                            if (ffrColor.r > 0.9 && ffrColor.g < 0.1 && ffrColor.b < 0.1) {
                                // Check if we already have a glow layer
                                let glowLayer = scene.getGlowLayerByName("FFRGlowLayer");

                                // Create glow layer if it doesn't exist
                                if (!glowLayer) {
                                    glowLayer = new BABYLON.GlowLayer("FFRGlowLayer", scene, {
                                        mainTextureFixedSize: 256, // Smaller texture for better performance
                                        blurKernelSize: 16 // Much smaller blur for subtle effect
                                    });
                                    glowLayer.intensity = 0.2; // Very subtle glow
                                }

                                // Add this section's submesh to the glow layer with custom color
                                const submeshIndex = i; // Current section index
                                const submesh = tunnelMesh.subMeshes.find(sm => sm.materialIndex === submeshIndex);
                                if (submesh) {
                                    // Use a very subtle red glow
                                    const glowColor = new BABYLON.Color3(0.3, 0.1, 0.1); // Very subtle red glow
                                    glowLayer.customEmissiveColorSelector = function(mesh, subMesh, material, result) {
                                        if (mesh === tunnelMesh && subMesh.materialIndex === submeshIndex) {
                                            result.set(glowColor.r, glowColor.g, glowColor.b, 1.0);
                                            return true;
                                        }
                                        return false;
                                    };
                                }
                            }
                        } catch (glowError) {
                            console.warn(`Could not create glow effect for tunnel section ${i}:`, glowError);
                        }
                    }
                }
            } catch (textureError) {
                console.warn(`Could not apply texture to tunnel section ${i}:`, textureError);
                // Fallback if texture fails - use FFR color directly
                sectionMaterial.diffuseColor = ffrColor;
                sectionMaterial.emissiveColor = ffrColor.scale(0.7);
            }

            // Add the material to the multi-material
            multiMaterial.subMaterials.push(sectionMaterial);
        }

        // Apply the multi-material to the mesh
        tunnelMesh.material = multiMaterial;

        // Create submeshes for each tunnel section
        let vertexStart = 0;
        let indexStart = 0;

        for (let i = 0; i < tunnelSections.length; i++) {
            const section = tunnelSections[i];
            const vertexCount = section.vertexCount || Math.floor(tunnelMesh.getTotalVertices() / tunnelSections.length);
            const indexCount = section.indexCount || Math.floor(tunnelMesh.getTotalIndices() / tunnelSections.length);

            new BABYLON.SubMesh(
                i, // material index
                vertexStart, // vertex start
                vertexCount, // vertex count
                indexStart, // index start
                indexCount, // index count
                tunnelMesh
            );

            vertexStart += vertexCount;
            indexStart += indexCount;
        }

        // Log performance metrics
        const endTime = performance.now();
        const totalTime = endTime - startTime;
        console.log(`Applied FFR-based coloring to tunnel mesh with ${tunnelSections.length} sections in ${totalTime.toFixed(2)}ms`);
    } catch (error) {
        console.error("Error applying FFR coloring to tunnel:", error);

        // Fallback to simple coloring if multi-material approach fails
        try {
            // Create a single PBR material with gradient texture based on FFR values
            const tunnelMaterial = new BABYLON.PBRMaterial("tunnelMaterial", tunnelMesh.getScene());
            tunnelMaterial.backFaceCulling = false;

            // Use the average FFR color
            const avgColor = ffrColors.reduce((acc, color) => acc.add(color), new BABYLON.Color3(0, 0, 0))
                .scale(1.0 / ffrColors.length);

            // Apply texture if available
            try {
                const scene = tunnelMesh.getScene();
                if (scene) {
                    // Ensure texture is loaded and applied
                    const texture = new BABYLON.Texture("./textures/others_0003_color_1k.jpg", scene, false, false, BABYLON.Texture.BILINEAR_SAMPLINGMODE);
                    if (texture) {
                        // Apply texture as albedo (base color)
                        tunnelMaterial.albedoTexture = texture;
                        tunnelMaterial.albedoTexture.uScale = 5;
                        tunnelMaterial.albedoTexture.vScale = 30;
                        tunnelMaterial.albedoTexture.hasAlpha = false;

                        // Set base material properties
                        tunnelMaterial.metallic = 0.5; // Semi-metallic surface
                        tunnelMaterial.roughness = 0.8; // Fairly rough surface

                        // Add normal map for better detail (using OpenGL format)
                        try {
                            const normalTexture = new BABYLON.Texture("./textures/others_0003_normal_opengl_1k.png", scene);
                            tunnelMaterial.normalTexture = normalTexture;
                            if (DEBUG_MODE) console.log("Applied OpenGL normal map to tunnel material");
                        } catch (normalError) {
                            console.warn("Could not apply normal map to tunnel:", normalError);
                        }

                        // Add height map for bump mapping
                        try {
                            const heightTexture = new BABYLON.Texture("./textures/others_0003_height_1k.png", scene);
                            tunnelMaterial.bumpTexture = heightTexture;
                            tunnelMaterial.bumpTexture.level = 0.8;
                            if (DEBUG_MODE) console.log("Applied height map for bump mapping");
                        } catch (heightError) {
                            console.warn("Could not apply height map to tunnel:", heightError);
                        }

                        // Add roughness texture for better realism
                        try {
                            const roughnessTexture = new BABYLON.Texture("./textures/others_0003_roughness_1k.jpg", scene);
                            tunnelMaterial.roughnessTexture = roughnessTexture;
                            if (DEBUG_MODE) console.log("Applied roughness map to tunnel material");
                        } catch (roughnessError) {
                            console.warn("Could not apply roughness map to tunnel:", roughnessError);
                        }

                        // Add ambient occlusion texture
                        try {
                            const aoTexture = new BABYLON.Texture("./textures/others_0003_ao_1k.jpg", scene);
                            tunnelMaterial.ambientTexture = aoTexture;
                            if (DEBUG_MODE) console.log("Applied ambient occlusion map to tunnel material");
                        } catch (aoError) {
                            console.warn("Could not apply ambient occlusion map to tunnel:", aoError);
                        }

                        // Add subsurface scattering texture
                        try {
                            const subsurfaceTexture = new BABYLON.Texture("./textures/others_0003_subsurface_1k.jpg", scene);
                            tunnelMaterial.subSurfaceTexture = subsurfaceTexture;
                            tunnelMaterial.subSurfaceIntensity = 0.5; // Adjust as needed
                            if (DEBUG_MODE) console.log("Applied subsurface scattering map to tunnel material");
                        } catch (subsurfaceError) {
                            console.warn("Could not apply subsurface scattering map to tunnel:", subsurfaceError);
                        }

                        // Keep texture as primary visual element
                        tunnelMaterial.albedoTexture.level = 1.0; // Full texture visibility

                        // Apply very subtle FFR color tint
                        const subtleAvgTint = avgColor.scale(0.2);
                        const neutralBase = new BABYLON.Color3(0.95, 0.95, 0.95);
                        tunnelMaterial.albedoColor = neutralBase.add(subtleAvgTint);

                        // Very subtle emissive color only if average is critical red
                        if (avgColor.r > 0.8 && avgColor.g < 0.2 && avgColor.b < 0.2) {
                            tunnelMaterial.emissiveColor = new BABYLON.Color3(0.1, 0.02, 0.02);
                            tunnelMaterial.emissiveIntensity = 0.1; // Very subtle glow
                        } else {
                            tunnelMaterial.emissiveColor = new BABYLON.Color3(0, 0, 0);
                            tunnelMaterial.emissiveIntensity = 0;
                        }

                        // Add a global point light to enhance the average FFR coloring
                        try {
                            // Create a point light at the center of the tunnel
                            const light = new BABYLON.PointLight(
                                "tunnelGlobalLight",
                                new BABYLON.Vector3(0, 0, 0), // Will be positioned later
                                scene
                            );

                            // Set light properties based on average FFR color
                            light.diffuse = avgColor.scale(1.5); // Increased from 1.0 to 1.5 for better visibility
                            light.specular = BABYLON.Color3.Black(); // No specular

                            // Set light intensity based on average FFR value
                            // Approximate FFR value from color components using improved formula for new color scheme
                            // Simplified version for average color
                            let ffrApprox;
                            if (avgColor.b > 0.5 && avgColor.g < 0.5) {
                                // Cyan to Blue range (FFR > 0.95)
                                ffrApprox = 0.97;
                            } else if (avgColor.g > 0.7 && avgColor.r < 0.3) {
                                // Green to Cyan range (0.90 < FFR ≤ 0.95)
                                ffrApprox = 0.92;
                            } else if (avgColor.g > 0.7) {
                                // Yellow-Green to Green range (0.80 < FFR ≤ 0.90)
                                ffrApprox = 0.85;
                            } else if (avgColor.r > 0.7 && avgColor.g > 0.7) {
                                // Yellow range (0.75 < FFR ≤ 0.80)
                                ffrApprox = 0.78;
                            } else if (avgColor.r > 0.7 && avgColor.g > 0.3) {
                                // Orange to Yellow-Orange range (0.60 < FFR ≤ 0.75)
                                ffrApprox = 0.68;
                            } else if (avgColor.r > 0.7 && avgColor.b < 0.1) {
                                // Red to Orange-Red range (0.50 < FFR ≤ 0.60)
                                ffrApprox = 0.55;
                            } else {
                                // Deep Red to Red range (FFR ≤ 0.50)
                                ffrApprox = 0.45;
                            }
                            const intensity = 0.8 * (1.0 - ffrApprox) + 0.3; // Increased from 0.6 to 0.8 for better visibility
                            light.intensity = intensity * 1.2; // Additional 20% boost for fallback mode

                            // Set moderate range to avoid overwhelming the scene
                            light.range = 30; // Reduced from 50 to 30 for more balanced lighting

                            // Position the light at the center of the tunnel
                            if (tunnelSections && tunnelSections.length > 0) {
                                const middleSection = tunnelSections[Math.floor(tunnelSections.length / 2)];
                                if (middleSection && middleSection.position) {
                                    light.position = middleSection.position.clone();
                                }
                            }

                            // Add metadata for cleanup
                            light.metadata = { type: "ffrGlobalLight" };
                        } catch (lightError) {
                            console.warn("Could not create global light for tunnel:", lightError);
                        }

                        // Add subtle fresnel effect to highlight edges with FFR color
                        tunnelMaterial.emissiveFresnelParameters = new BABYLON.FresnelParameters();
                        tunnelMaterial.emissiveFresnelParameters.bias = 0.2; // Increased for more focused edge effect
                        tunnelMaterial.emissiveFresnelParameters.power = 1.0; // Increased for sharper falloff
                        tunnelMaterial.emissiveFresnelParameters.leftColor = BABYLON.Color3.White();
                        tunnelMaterial.emissiveFresnelParameters.rightColor = avgColor.scale(0.7); // Reduced from 2.0 to 0.7 for subtle edge highlighting

                        // Add subtle glow layer for the entire tunnel
                        try {
                            // Create glow layer with moderate settings
                            const glowLayer = new BABYLON.GlowLayer("FFRGlowLayer", scene, {
                                mainTextureFixedSize: 512,
                                blurKernelSize: 24 // Reduced from 32 to 24 for less intense blur
                            });
                            glowLayer.intensity = 0.3; // Reduced from 0.8 to 0.3 for more subtle glow

                            // Use a moderate version of the average FFR color for the glow
                            const glowColor = avgColor.scale(1.0); // Reduced from 2.5 to 1.0 for more subtle glow
                            glowLayer.customEmissiveColorSelector = function(mesh, subMesh, material, result) {
                                if (mesh === tunnelMesh) {
                                    result.set(glowColor.r, glowColor.g, glowColor.b, 1.0);
                                    return true;
                                }
                                return false;
                            };
                        } catch (glowError) {
                            console.warn("Could not create glow effect for tunnel:", glowError);
                        }
                    }
                }
            } catch (textureError) {
                console.warn("Could not apply texture to tunnel in fallback mode:", textureError);
                // Fallback if texture fails - use FFR color directly
                tunnelMaterial.albedoColor = avgColor;
                tunnelMaterial.emissiveColor = avgColor.scale(0.7);
                tunnelMaterial.emissiveIntensity = 0.3;

                // Set PBR material properties
                tunnelMaterial.metallic = 0.5;
                tunnelMaterial.roughness = 0.8;
            }

            tunnelMaterial.backFaceCulling = false;

            // Apply the material to the mesh
            tunnelMesh.material = tunnelMaterial;

            // Create a single submesh for the entire tunnel
            new BABYLON.SubMesh(
                0, // material index
                0, // vertex start
                tunnelMesh.getTotalVertices(), // vertex count
                0, // index start
                tunnelMesh.getTotalIndices(), // index count
                tunnelMesh
            );

            // Log performance metrics for fallback
            const fallbackEndTime = performance.now();
            const fallbackTime = fallbackEndTime - startTime;
            console.log(`Applied fallback FFR coloring to tunnel mesh in ${fallbackTime.toFixed(2)}ms`);
        } catch (fallbackError) {
            console.error("Error applying fallback coloring to tunnel:", fallbackError);
        }
    }
}

/**
 * Cleans up FFR-related resources (lights, glow layers)
 * @param {BABYLON.Scene} scene - The scene containing the resources
 */
function cleanupFFRResources(scene) {
    if (!scene) {
        console.warn("Cannot cleanup FFR resources: No scene provided");
        return;
    }

    // Clean up FFR lights
    const lights = scene.lights;
    if (lights) {
        for (let i = lights.length - 1; i >= 0; i--) {
            const light = lights[i];
            if (light && light.metadata) {
                if (light.metadata.type === "ffrLight" || light.metadata.type === "ffrGlobalLight") {
                    light.dispose();
                }
            }
        }
    }

    // Clean up glow layer
    const glowLayer = scene.getGlowLayerByName("FFRGlowLayer");
    if (glowLayer) {
        glowLayer.dispose();
    }

    if (typeof DEBUG_MODE !== 'undefined' && DEBUG_MODE) {
        console.log("FFR resources cleaned up");
    }
}

/**
 * Debug function to test FFR color generation
 * @param {number} testFFR - FFR value to test (0-1)
 * @returns {BABYLON.Color3} The generated color
 */
function debugFFRColor(testFFR) {
    const color = getColorForFFR(testFFR);
    console.log(`🎨 FFR ${testFFR.toFixed(2)} -> RGB(${color.r.toFixed(2)}, ${color.g.toFixed(2)}, ${color.b.toFixed(2)})`);
    return color;
}

/**
 * Test all FFR color ranges
 */
function testAllFFRColors() {
    console.log("🎨 Testing FFR color generation:");

    // Test critical stenosis (red)
    debugFFRColor(0.30); // Deep red
    debugFFRColor(0.45); // Red

    // Test severe stenosis (red to orange)
    debugFFRColor(0.55); // Orange-red

    // Test moderate stenosis (orange)
    debugFFRColor(0.65); // Orange

    // Test mild stenosis (yellow-orange)
    debugFFRColor(0.72); // Yellow-orange

    // Test borderline (yellow)
    debugFFRColor(0.78); // Yellow

    // Test normal (green)
    debugFFRColor(0.85); // Yellow-green
    debugFFRColor(0.88); // Green

    // Test good flow (cyan)
    debugFFRColor(0.92); // Cyan

    // Test excellent flow (blue)
    debugFFRColor(0.97); // Blue
}

// functions for use in other modules
window.calculateTunnelFFR = calculateTunnelFFR;
window.applyFFRColoring = applyFFRColoring;
window.cleanupFFRResources = cleanupFFRResources;
window.debugFFRColor = debugFFRColor;
window.testAllFFRColors = testAllFFRColors;
window.getColorForFFR = getColorForFFR;
