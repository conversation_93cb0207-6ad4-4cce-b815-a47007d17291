// atheroscleroticPlaques.js - System zmian miażdżycowych (3D klastry)

// Check if BABYLON is available
if (typeof BABYLON === 'undefined') {
    console.warn('BABYLON.js is not loaded. Atherosclerotic plaques functionality will be limited.');
}

// Tablica przechowująca wszystkie zmiany miażdżycowe
let atheroscleroticPlaques = [];

// Zapamiętywanie napotkanych typów blaszek przez gracza
const encounteredPlaqueTypes = new Set();

// Tekstura plastra miodu dla zmian miażdżycowych
let honeycombTexture = null;

// Konfiguracja zmian miażdżycowych
const PLAQUE_CONFIG = {
    // Liczba klastrów zmian miażdżycowych na poziom trudności - zwiększono dla większej intensywności
    CLUSTERS_PER_LEVEL: {
        EASY: 5,    // Zwiększono z 3 do 5 (Poziomy 1-6)
        MEDIUM: 8,  // Zwiększono z 5 do 8 (Poziomy 7-12)
        HARD: 12    // Zwiększono z 8 do 12 (Poziomy 13-18)
    },

    // Liczba zmian w klastrze - zwiększono dla większej intensywności
    PLAQUES_PER_CLUSTER: {
        MIN: 4,     // Zwiększono z 3 do 4
        MAX: 12     // Zwiększono z 8 do 12
    },

    // Rozmiary zmian miażdżycowych
    SIZE: {
        MIN: 0.08,
        MAX: 0.25
    },

    // Kolory dla różnych typów zmian miażdżycowych
    COLORS: {
        // Zmiany zwapniałe (białe/szare)
        CALCIFIED: {
            BASE: typeof BABYLON !== 'undefined' ? new BABYLON.Color3(0.9, 0.9, 0.9) : {r: 0.9, g: 0.9, b: 0.9},
            EMISSION: typeof BABYLON !== 'undefined' ? new BABYLON.Color3(0.6, 0.6, 0.7) : {r: 0.6, g: 0.6, b: 0.7},
            DAMAGE: 15
        },
        // Zmiany lipidowe (żółte/pomarańczowe)
        LIPID: {
            BASE: typeof BABYLON !== 'undefined' ? new BABYLON.Color3(0.9, 0.7, 0.2) : {r: 0.9, g: 0.7, b: 0.2},
            EMISSION: typeof BABYLON !== 'undefined' ? new BABYLON.Color3(0.8, 0.5, 0.1) : {r: 0.8, g: 0.5, b: 0.1},
            DAMAGE: 10
        },
        // Zmiany włókniste (czerwonawe)
        FIBROUS: {
            BASE: typeof BABYLON !== 'undefined' ? new BABYLON.Color3(0.8, 0.3, 0.3) : {r: 0.8, g: 0.3, b: 0.3},
            EMISSION: typeof BABYLON !== 'undefined' ? new BABYLON.Color3(0.6, 0.2, 0.2) : {r: 0.6, g: 0.2, b: 0.2},
            DAMAGE: 8
        }
    },

    // Prawdopodobieństwo wystąpienia poszczególnych typów zmian
    TYPE_PROBABILITY: {
        CALCIFIED: 0.3,
        LIPID: 0.4,
        FIBROUS: 0.3
    },

    // Odległość od ściany tunelu (0 = na ścianie, 1 = w centrum)
    WALL_DISTANCE: {
        MIN: 0.05,
        MAX: 0.15
    },

    // Parametry kolizji
    COLLISION: {
        COOLDOWN: 300,  // ms między kolizjami
        DAMAGE_MULTIPLIER: 1.2  // mnożnik obrażeń
    },

    // Nowe parametry 3D
    CLUSTER_3D: {
        SPHERE_DISTRIBUTION_RADIUS: 0.85,  // 85% promienia dla rozmieszczenia centrów sfer
        MAX_INDIVIDUAL_SPHERE_RATIO: 0.25, // 25% promienia klastra dla pojedynczej sfery
        UNIFORM_SCALE_BASE: 0.85,          // Bazowe skalowanie (85%)
        UNIFORM_SCALE_VARIATION: 0.3,      // Wariacja skalowania (30%)
        SCALE_AXIS_VARIATION: 0.15,        // Maksymalna różnica między osiami (15%)
        DEFORMATION_INTENSITY: 0.4,        // Intensywność deformacji organicznej
        POSITION_OFFSET_RATIO: 0.3         // Stosunek offsetu pozycji do rozmiaru
    }
};

// Klasyfikacja AHA – typy blaszek miażdżycowych
const PLAQUE_TYPES = [
  {
    type: 1,
    name: "Initial lesion",
    size: [0.4, 0.7],
    destructible: true,
    color: "#e0e0e0",
    texture: "textures/type1.jpg",
    description: "Wczesne złogi cholesterolu, łatwe do usunięcia.",
    visualHint: "Mała, półprzezroczysta plamka",
    gameplayNote: "Shoot to destroy",
    hitPoints: 1
  },
  {
    type: 2,
    name: "Fatty streak",
    size: [0.5, 0.8],
    destructible: true,
    color: "#ffe066",
    texture: "textures/type2.jpg",
    description: "Żółtawa smuga, umiarkowanie zwęża światło naczynia.",
    visualHint: "Podłużna, żółtawa smuga",
    gameplayNote: "Shoot to destroy",
    hitPoints: 1
  },
  {
    type: 3,
    name: "Intermediate lesion",
    size: [0.6, 0.9],
    destructible: true,
    color: "#ffd166",
    texture: "textures/type3.jpg",
    description: "Miękka, lekko wystająca blaszka, spowalnia gracza.",
    visualHint: "Wybrzuszona, lekko uniesiona plama",
    gameplayNote: "Shoot to destroy",
    hitPoints: 2
  },
  {
    type: 4,
    name: "Atheroma",
    size: [0.7, 1.0],
    destructible: false,
    color: "#ff8c42",
    texture: "textures/type4.jpg",
    description: "Wyraźna, miękka blaszka – nie do zniszczenia.",
    visualHint: "Wyraźne, miękkie wybrzuszenie",
    gameplayNote: "Must be avoided",
    damage: 15
  },
  {
    type: 5,
    name: "Fibroatheroma",
    size: [0.8, 1.1],
    destructible: false,
    color: "#bdbdbd",
    texture: "textures/type5.jpg",
    description: "Twarda, często zwapniała blaszka – odbija gracza.",
    visualHint: "Twardy, biały lub zwapniały guzek",
    gameplayNote: "Must be avoided",
    damage: 20
  },
  {
    type: 6,
    name: "Complicated lesion",
    size: [0.9, 1.2],
    destructible: false,
    color: "#d7263d",
    texture: "textures/type6.jpg",
    description: "Nieregularna, z zakrzepem – najgroźniejsza.",
    visualHint: "Duża, nieregularna z widocznym skrzepliną",
    gameplayNote: "Must be avoided",
    damage: 30
  }
];

// Ostatni czas kolizji ze zmianą miażdżycową
let lastPlaqueCollisionTime = 0;

/**
 * Generuje zmiany miażdżycowe wzdłuż tunelu
 * @param {Array} tunnelSections - Sekcje tunelu
 * @param {number} gameLevel - Aktualny poziom gry
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function generateAtheroscleroticPlaques(tunnelSections, gameLevel, scene) {
    if (!tunnelSections || tunnelSections.length === 0 || !scene) {
        console.error("Nie można wygenerować zmian miażdżycowych - brak sekcji tunelu lub sceny");
        return;
    }

    // Wyczyść istniejące zmiany miażdżycowe
    clearAtheroscleroticPlaques();

    // Określ liczbę klastrów na podstawie poziomu trudności
    let clustersCount;
    if (gameLevel <= 6) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.EASY;
    } else if (gameLevel <= 12) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.MEDIUM;
    } else {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.HARD;
    }

    console.log(`Generowanie ${clustersCount} klastrów zmian miażdżycowych dla poziomu ${gameLevel}`);

    // Wybierz losowe sekcje tunelu dla klastrów (z wyłączeniem pierwszych i ostatnich 10%)
    const validSectionIndices = [];
    const startExcludeCount = Math.floor(tunnelSections.length * 0.1);
    const endExcludeCount = Math.floor(tunnelSections.length * 0.1);

    for (let i = startExcludeCount; i < tunnelSections.length - endExcludeCount; i++) {
        validSectionIndices.push(i);
    }

    // Wymieszaj indeksy, aby uzyskać losowe rozmieszczenie
    shuffleArray(validSectionIndices);

    // Wybierz sekcje dla klastrów
    const clusterSectionIndices = validSectionIndices.slice(0, clustersCount);

    // Generuj klastry zmian miażdżycowych
    for (let i = 0; i < clusterSectionIndices.length; i++) {
        const sectionIndex = clusterSectionIndices[i];
        const section = tunnelSections[sectionIndex];

        // Określ liczbę zmian w klastrze
        const plaquesInCluster = Math.floor(
            PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN +
            Math.random() * (PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MAX - PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN + 1)
        );

        // Generuj zmiany w klastrze
        generatePlaqueCluster(section, plaquesInCluster, scene);
    }

    console.log(`Wygenerowano ${atheroscleroticPlaques.length} zmian miażdżycowych w ${clustersCount} klastrach`);
}

/**
 * Ulepszona funkcja generująca klaster zmian miażdżycowych w pełnej 3D przestrzeni tunelu
 * @param {Object} section - Sekcja tunelu
 * @param {number} plaquesCount - Liczba zmian w klastrze
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function generatePlaqueCluster(section, plaquesCount, scene) {
    if (!section || !section.centerPoint || !section.radius) {
        console.warn("Nieprawidłowa sekcja tunelu dla klastra zmian miażdżycowych");
        return;
    }

    // KLUCZOWA ZMIANA: Generowanie pozycji w pełnej 3D przestrzeni zamiast płaskiego rozmieszczenia

    // 1. Wybierz typ rozmieszczenia klastra
    const clusterType = Math.random();
    let plaquePositions = [];

    if (clusterType < 0.4) {
        // Typ 1: Rozmieszczenie sferyczne (40% szans)
        plaquePositions = generateSphericalCluster(section, plaquesCount);
    } else if (clusterType < 0.7) {
        // Typ 2: Rozmieszczenie elipsoidalne (30% szans)
        plaquePositions = generateEllipsoidalCluster(section, plaquesCount);
    } else {
        // Typ 3: Rozmieszczenie warstwowe/spiralne (30% szans)
        plaquePositions = generateLayeredCluster(section, plaquesCount);
    }

    // 2. Utwórz zmiany miażdżycowe na wygenerowanych pozycjach
    for (let i = 0; i < plaquePositions.length; i++) {
        const position = plaquePositions[i];

        // Losowy rozmiar zmiany z lepszą kontrolą proporcji
        const baseDiameter = PLAQUE_CONFIG.SIZE.MIN +
            Math.random() * (PLAQUE_CONFIG.SIZE.MAX - PLAQUE_CONFIG.SIZE.MIN);

        // Dostosuj rozmiar do wielkości tunelu dla lepszych proporcji
        const tunnelSizeFactor = Math.min(1.2, section.radius / 2.0);
        const plaqueDiameter = baseDiameter * tunnelSizeFactor;

        // Losowy typ zmiany miażdżycowej
        const plaqueType = getRandomPlaqueType();

        // Utwórz zmianę miażdżycową
        createPlaque(position, plaqueDiameter, plaqueType, scene);
    }
}

/**
 * Ulepszona funkcja tworząca pojedynczą zmianę miażdżycową z gwarantowaną 3D strukturą
 * @param {BABYLON.Vector3} position - Pozycja zmiany
 * @param {number} size - Rozmiar zmiany
 * @param {string} type - Typ zmiany (CALCIFIED, LIPID, FIBROUS)
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function createPlaque(position, size, type, scene) {
    // Określ typ AHA na podstawie typu zmiany
    let ahaType;
    if (type === 'CALCIFIED') {
        ahaType = Math.random() < 0.7 ? 5 : 6;
    } else if (type === 'LIPID') {
        ahaType = Math.random() < 0.6 ? 2 : (Math.random() < 0.5 ? 1 : 3);
    } else {
        ahaType = Math.random() < 0.6 ? 3 : (Math.random() < 0.5 ? 4 : 5);
    }
    const ahaTypeInfo = PLAQUE_TYPES.find(t => t.type === ahaType);

    // --- KLASTER 3D: Zapewnienie pełnej trójwymiarowości ---
    const cluster = new BABYLON.TransformNode(`plaqueCluster_${type}_${atheroscleroticPlaques.length}`, scene);

    // Zwiększona liczba sfer dla bardziej złożonego klastra
    const spheresCount = 12 + Math.floor(Math.random() * 8); // 12-19 sfer

    // Właściwy promień klastra - zapewniamy że wszystkie wymiary są równorzędne
    const maxClusterRadius = size / 2;

    // KLUCZOWA ZMIANA: Używamy pełnej sfery do rozmieszczenia sfer
    // zamiast koncentrowania się na jednej płaszczyźnie
    const distributionSphereRadius = maxClusterRadius * PLAQUE_CONFIG.CLUSTER_3D.SPHERE_DISTRIBUTION_RADIUS;
    const maxIndividualSphereRadius = maxClusterRadius * PLAQUE_CONFIG.CLUSTER_3D.MAX_INDIVIDUAL_SPHERE_RATIO;

    // Generuj punkty na sferze używając algorytmu Fibonacci Sphere dla równomiernego rozmieszczenia
    const goldenRatio = (1 + Math.sqrt(5)) / 2;
    const spherePositions = [];

    for (let i = 0; i < spheresCount; i++) {
        // Algorytm Fibonacci Sphere zapewnia równomierne rozmieszczenie na sferze
        const y = 1 - (i / (spheresCount - 1)) * 2; // y od 1 do -1
        const radiusAtY = Math.sqrt(1 - y * y);

        const theta = 2 * Math.PI * i / goldenRatio;

        const x = Math.cos(theta) * radiusAtY;
        const z = Math.sin(theta) * radiusAtY;

        // Dodaj losowe przesunięcie dla mniej regularnego układu
        const randomOffset = 0.3; // 30% losowego przesunięcia
        const offsetX = x + (Math.random() - 0.5) * randomOffset;
        const offsetY = y + (Math.random() - 0.5) * randomOffset;
        const offsetZ = z + (Math.random() - 0.5) * randomOffset;

        // Normalizuj i przeskaluj do promienia rozmieszczenia
        const length = Math.sqrt(offsetX * offsetX + offsetY * offsetY + offsetZ * offsetZ);
        const normalizedX = (offsetX / length) * distributionSphereRadius;
        const normalizedY = (offsetY / length) * distributionSphereRadius;
        const normalizedZ = (offsetZ / length) * distributionSphereRadius;

        spherePositions.push({
            x: normalizedX,
            y: normalizedY,
            z: normalizedZ
        });
    }

    for (let i = 0; i < spheresCount; i++) {
        const spherePos = spherePositions[i];

        // Rozmiar indywidualnej sfery z większą kontrolą
        const individualSphereDiameter = maxIndividualSphereRadius * (1.2 + Math.random() * 0.8); // 1.2-2.0 względem base

        // Tworzenie różnorodnych 3D kształtów
        let sphere;
        const shapeType = Math.random();

        if (shapeType < 0.2) {
            // Dodecahedron
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaquePolyhedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 2, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.35) {
            // Icosahedron
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaqueIcosahedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 1, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.5) {
            // Octahedron
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaqueOctahedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 0, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.8) {
            // Deformowana sfera
            sphere = BABYLON.MeshBuilder.CreateSphere(
                `plaqueSphere_${type}_${atheroscleroticPlaques.length}_${i}`,
                { diameter: individualSphereDiameter, segments: 24 },
                scene
            );

            // Zaawansowana deformacja dla organicznego wyglądu
            applyOrganicDeformation(sphere, PLAQUE_CONFIG.CLUSTER_3D.DEFORMATION_INTENSITY);
        } else {
            // Elipsoida z pełną 3D deformacją
            sphere = BABYLON.MeshBuilder.CreateSphere(
                `plaqueEllipsoid_${type}_${atheroscleroticPlaques.length}_${i}`,
                { diameter: individualSphereDiameter, segments: 20 },
                scene
            );

            // KLUCZOWA ZMIANA: Równomierne skalowanie we wszystkich 3 wymiarach
            const baseScale = PLAQUE_CONFIG.CLUSTER_3D.UNIFORM_SCALE_BASE + Math.random() * 0.6; // 0.85-1.45 bazowa skala
            const scaleVariation = PLAQUE_CONFIG.CLUSTER_3D.SCALE_AXIS_VARIATION; // Maksymalna wariacja między osiami

            const scaleX = baseScale + (Math.random() - 0.5) * scaleVariation;
            const scaleY = baseScale + (Math.random() - 0.5) * scaleVariation;
            const scaleZ = baseScale + (Math.random() - 0.5) * scaleVariation;

            sphere.scaling = new BABYLON.Vector3(scaleX, scaleY, scaleZ);
        }

        sphere.parent = cluster;
        sphere.position = new BABYLON.Vector3(spherePos.x, spherePos.y, spherePos.z);

        // Dodaj pełną 3D rotację każdej sfery
        sphere.rotation.x = Math.random() * Math.PI * 2;
        sphere.rotation.y = Math.random() * Math.PI * 2;
        sphere.rotation.z = Math.random() * Math.PI * 2;

        // Materiał z zaawansowanymi efektami 3D
        const mat = create3DMaterial(type, ahaTypeInfo, scene, i);
        sphere.material = mat;

        // Właściwości 3D
        sphere.receiveShadows = true;
        sphere.checkCollisions = true;
        sphere.isPickable = true;
    }

    // KLUCZOWA ZMIANA: Pozycjonowanie klastra w pełnej 3D przestrzeni tunelu
    cluster.position = calculate3DPositionInTunnel(position, size);

    // Pełna 3D rotacja klastra dla maksymalnej różnorodności
    cluster.rotation = new BABYLON.Vector3(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
    );

    // KLUCZOWA ZMIANA: Równomierne skalowanie we wszystkich wymiarach
    const uniformScale = PLAQUE_CONFIG.CLUSTER_3D.UNIFORM_SCALE_BASE + Math.random() * PLAQUE_CONFIG.CLUSTER_3D.UNIFORM_SCALE_VARIATION;
    const scaleVariation = PLAQUE_CONFIG.CLUSTER_3D.SCALE_AXIS_VARIATION; // Maksymalna wariacja między osiami

    const clusterScaleX = uniformScale + (Math.random() - 0.5) * scaleVariation;
    const clusterScaleY = uniformScale + (Math.random() - 0.5) * scaleVariation;
    const clusterScaleZ = uniformScale + (Math.random() - 0.5) * scaleVariation;

    cluster.scaling = new BABYLON.Vector3(clusterScaleX, clusterScaleY, clusterScaleZ);

    // Zapisz info o klastrze
    atheroscleroticPlaques.push({
        mesh: cluster,
        type: type,
        ahaType: ahaType,
        damage: (ahaTypeInfo && ahaTypeInfo.damage) ? ahaTypeInfo.damage : 10,
        size: size,
        originalPosition: cluster.position.clone(),
        destructible: ahaTypeInfo ? ahaTypeInfo.destructible : false,
        hitPoints: ahaTypeInfo && ahaTypeInfo.hitPoints ? ahaTypeInfo.hitPoints : 0,
        currentHitPoints: ahaTypeInfo && ahaTypeInfo.hitPoints ? ahaTypeInfo.hitPoints : 0
    });
}

/**
 * Zwraca losowy typ zmiany miażdżycowej na podstawie zdefiniowanych prawdopodobieństw
 * @returns {string} Typ zmiany (CALCIFIED, LIPID, FIBROUS)
 */
function getRandomPlaqueType() {
    const rand = Math.random();
    if (rand < PLAQUE_CONFIG.TYPE_PROBABILITY.CALCIFIED) {
        return 'CALCIFIED';
    } else if (rand < PLAQUE_CONFIG.TYPE_PROBABILITY.CALCIFIED + PLAQUE_CONFIG.TYPE_PROBABILITY.LIPID) {
        return 'LIPID';
    } else {
        return 'FIBROUS';
    }
}

/**
 * Wywołuje komunikat edukacyjny o typie blaszki
 * @param {number} typeNum - Numer typu blaszki
 */
function showPlaqueTypeInfo(typeNum) {
    const typeObj = PLAQUE_TYPES.find(t => t.type === typeNum);
    if (!typeObj) return;
    if (window.ui && typeof window.ui.showPlaqueTypeMessage === 'function') {
        window.ui.showPlaqueTypeMessage(
            typeObj.name,
            typeObj.description,
            `${typeObj.visualHint} | ${typeObj.destructible ? 'Można zniszczyć strzałem' : 'Nie do zniszczenia, należy omijać'}`
        );
    } else {
        // Fallback: jeśli UI nie jest gotowe, nie pokazuj alertu
    }
}

/**
 * Pomocnicza: zamiana typu tekstowego na numer AHA
 * @param {string} typeStr - Typ zmiany jako tekst
 * @returns {number} Numer typu zmiany
 */
function getPlaqueTypeNumber(typeStr) {
    switch(typeStr) {
        case 'CALCIFIED': return 5;
        case 'LIPID': return 2;
        case 'FIBROUS': return 3;
        default: {
            const found = PLAQUE_TYPES.find(t => t.type && t.type.toString() === typeStr.toString());
            return found ? found.type : 0;
        }
    }
}

/**
 * Aktualizuje zmiany miażdżycowe (animacje, efekty)
 * @param {number} deltaTime - Czas od ostatniej klatki w sekundach
 */
function updateAtheroscleroticPlaques(deltaTime) {
    // Flaga kontrolująca animację (domyślnie włączona)
    if (typeof window.animationEnabled === 'undefined') {
        window.animationEnabled = true;
    }

    // Jeśli animacja jest wyłączona, nie aktualizuj
    if (!window.animationEnabled) return;

    const now = performance.now();

    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (!plaque.mesh || plaque.mesh.isDisposed()) continue;

        // Inicjalizuj parametry animacji, jeśli nie istnieją
        if (!plaque.animParams) {
            plaque.animParams = {
                rotationSpeed: 0.0005 + Math.random() * 0.001,
                pulseFreq: 0.0005 + Math.random() * 0.001,
                pulseAmp: 0.03 + Math.random() * 0.04,
                wobbleFreq: 0.0007 + Math.random() * 0.0006,
                wobbleAmp: 0.03 + Math.random() * 0.03,
                phaseOffset: Math.random() * Math.PI * 2,
                originalScale: plaque.mesh.scaling.clone()
            };
        }

        // Zaawansowana animacja 3D dla wszystkich typów blaszek

        // 1. Płynna rotacja całego klastra
        if (plaque.mesh.rotationQuaternion) {
            // Jeśli używamy quaternionów, zastosuj płynną rotację
            const rotAngle = now * plaque.animParams.rotationSpeed;
            const newRotation = BABYLON.Quaternion.RotationAxis(
                new BABYLON.Vector3(0, 1, 0),
                rotAngle
            );
            // Zachowaj oryginalną rotację i dodaj nową
            if (!plaque.originalRotation) {
                plaque.originalRotation = plaque.mesh.rotationQuaternion.clone();
            }
            plaque.mesh.rotationQuaternion = plaque.originalRotation.multiply(newRotation);
        } else {
            // Fallback dla standardowej rotacji
            plaque.mesh.rotation.y += deltaTime * plaque.animParams.rotationSpeed * 20;
        }

        // 2. Organiczne falowanie pozycji
        if (plaque.originalPosition) {
            // Złożony ruch falowy w 3D
            const timeOffset = now * plaque.animParams.wobbleFreq + plaque.animParams.phaseOffset;
            plaque.mesh.position.x = plaque.originalPosition.x + Math.sin(timeOffset) * plaque.animParams.wobbleAmp;
            plaque.mesh.position.y = plaque.originalPosition.y + Math.cos(timeOffset * 1.3) * plaque.animParams.wobbleAmp;
            plaque.mesh.position.z = plaque.originalPosition.z + Math.sin(timeOffset * 0.7) * plaque.animParams.wobbleAmp;
        }

        // 3. Typ-specyficzne animacje z ulepszonymi efektami 3D
        if (plaque.type === 'LIPID') {
            // Dla zmian lipidowych - organiczne pulsowanie w 3D
            const pulseFactor = Math.sin(now * plaque.animParams.pulseFreq) * plaque.animParams.pulseAmp + 1;

            // Asymetryczne pulsowanie dla lepszego efektu 3D
            plaque.mesh.scaling.x = plaque.animParams.originalScale.x * (pulseFactor * 0.9);
            plaque.mesh.scaling.y = plaque.animParams.originalScale.y * (pulseFactor * 1.1);
            plaque.mesh.scaling.z = plaque.animParams.originalScale.z * pulseFactor;

            // Dodatkowa animacja dzieci (sfer w klastrze)
            const children = plaque.mesh.getChildren();
            for (let j = 0; j < children.length; j++) {
                const child = children[j];
                if (child.scaling) {
                    // Każde dziecko pulsuje nieco inaczej
                    const childPulse = Math.sin(now * plaque.animParams.pulseFreq + j) * 0.03 + 1;
                    child.scaling.x *= childPulse;
                    child.scaling.y *= childPulse;
                    child.scaling.z *= childPulse;
                }
            }
        }
        else if (plaque.type === 'CALCIFIED') {
            // Dla zmian zwapniałych - powolna, nieregularna rotacja
            // Animuj dzieci (sfery/wielościany w klastrze)
            const children = plaque.mesh.getChildren();
            for (let j = 0; j < children.length; j++) {
                const child = children[j];
                if (child.rotation) {
                    // Każde dziecko obraca się nieco inaczej
                    child.rotation.x += deltaTime * (0.01 + j * 0.005);
                    child.rotation.z += deltaTime * (0.015 - j * 0.002);
                }
            }
        }
        else if (plaque.type === 'FIBROUS') {
            // Dla zmian włóknistych - delikatne, złożone drgania
            // Zastosuj efekt fali przechodzący przez klaster
            const children = plaque.mesh.getChildren();
            for (let j = 0; j < children.length; j++) {
                const child = children[j];
                if (child.position) {
                    // Efekt fali przesuwającej się przez klaster
                    const waveOffset = now * 0.001 + j * 0.5;
                    const waveAmp = 0.005 + (j % 3) * 0.002;

                    // Zachowaj oryginalną pozycję dziecka
                    if (!child.originalPosition) {
                        child.originalPosition = child.position.clone();
                    }

                    // Zastosuj ruch falowy
                    child.position.x = child.originalPosition.x + Math.sin(waveOffset) * waveAmp;
                    child.position.y = child.originalPosition.y + Math.cos(waveOffset * 1.2) * waveAmp;
                    child.position.z = child.originalPosition.z + Math.sin(waveOffset * 0.8) * waveAmp;
                }
            }
        }
    }
}

/**
 * Przełącza animację zmian miażdżycowych
 * @returns {boolean} Nowy stan animacji
 */
function toggleAnimation() {
    window.animationEnabled = !window.animationEnabled;
    console.log(`Animacja ${window.animationEnabled ? 'włączona' : 'wyłączona'}`);
    return window.animationEnabled;
}

/**
 * Sprawdza kolizje gracza ze zmianami miażdżycowymi
 * @param {BABYLON.AbstractMesh} playerMesh - Mesh gracza (bunnyCollider)
 * @returns {number} Obrażenia zadane graczowi (0 jeśli brak kolizji)
 */
function checkPlaqueCollisions(playerMesh) {
    if (!playerMesh || !Array.isArray(atheroscleroticPlaques)) return 0;

    const now = Date.now();
    // Sprawdź cooldown kolizji
    if (now - lastPlaqueCollisionTime < PLAQUE_CONFIG.COLLISION.COOLDOWN) {
        return 0;
    }

    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (!plaque.mesh || plaque.mesh.isDisposed()) continue;

        // Sprawdź kolizję
        let collision = improvedPlaqueCollisionDetection(playerMesh, plaque);

        if (collision) {
            // Aktualizuj czas ostatniej kolizji
            lastPlaqueCollisionTime = now;

            // Wyświetl info o typie blaszki przy pierwszym kontakcie
            const typeNum = plaque.ahaType || getPlaqueTypeNumber(plaque.type);
            if (!encounteredPlaqueTypes.has(typeNum)) {
                encounteredPlaqueTypes.add(typeNum);
                showPlaqueTypeInfo(typeNum);
            }

            // Znajdź informacje o typie AHA
            const ahaTypeInfo = PLAQUE_TYPES.find(t => t.type === typeNum);

            // Oblicz obrażenia
            let damage = 0;
            if (ahaTypeInfo && ahaTypeInfo.damage) {
                damage = ahaTypeInfo.damage * PLAQUE_CONFIG.COLLISION.DAMAGE_MULTIPLIER;
            } else {
                damage = plaque.damage * PLAQUE_CONFIG.COLLISION.DAMAGE_MULTIPLIER;
            }

            // Efekty kolizji
            if (typeof playParticleEffect === 'function') {
                playParticleEffect("obstacleCollision", playerMesh.position);
            }

            // Dodaj efekt odrzutu dla zmiany miażdżycowej
            const impulseDirection = plaque.mesh.position.subtract(playerMesh.position).normalize();
            plaque.mesh.position.addInPlace(impulseDirection.scale(0.05));

            console.log(`Kolizja ze zmianą miażdżycową typu AHA ${typeNum} (${plaque.type}). Obrażenia: ${damage.toFixed(1)}`);

            return damage;
        }
    }

    return 0;
}

/**
 * Czyści wszystkie zmiany miażdżycowe
 */
function clearAtheroscleroticPlaques() {
    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (plaque.mesh && !plaque.mesh.isDisposed()) {
            // Usuń efekt świecenia, jeśli istnieje
            const glowLayer = plaque.mesh.getScene().getGlowLayerByName(`plaqueGlow_${i}`);
            if (glowLayer) {
                glowLayer.dispose();
            }

            // Usuń materiał
            if (plaque.mesh.material) {
                plaque.mesh.material.dispose();
            }

            // Usuń mesh
            plaque.mesh.dispose();
        }
    }

    // Wyczyść tablicę
    atheroscleroticPlaques = [];
    console.log("Wyczyszczono wszystkie zmiany miażdżycowe");
}

/**
 * Pomocnicza funkcja do mieszania tablicy (algorytm Fisher-Yates)
 * @param {Array} array - Tablica do wymieszania
 */
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

/**
 * Sprawdza kolizje laserów ze zmianami miażdżycowymi
 * @returns {void}
 */
function checkLaserPlaqueCollisions() {
    // Sprawdź czy wszystkie wymagane zmienne są dostępne
    if (!Array.isArray(lasers) || !Array.isArray(atheroscleroticPlaques)) return;
    if (typeof scene === 'undefined' || !scene) return;
    if (typeof gameRunning === 'undefined' || !gameRunning) return;

    for (let i = lasers.length - 1; i >= 0; i--) {
        const laser = lasers[i];
        if (!laser || !laser.mesh || laser.mesh.isDisposed()) continue;

        for (let j = atheroscleroticPlaques.length - 1; j >= 0; j--) {
            const plaque = atheroscleroticPlaques[j];
            if (!plaque || !plaque.mesh || plaque.mesh.isDisposed()) continue;

            // Sprawdź czy blaszka jest zniszczalna
            if (!plaque.destructible) continue;

            // Prosta detekcja kolizji na podstawie odległości
            const distance = BABYLON.Vector3.Distance(
                laser.mesh.position,
                plaque.mesh.position
            );

            // Jeśli wykryto kolizję
            if (distance < plaque.size * 1.2) {
                // Zmniejsz punkty wytrzymałości blaszki
                plaque.currentHitPoints--;

                // Efekty trafienia
                if (typeof playSoundEffect === 'function') {
                    // Użyj dostępnego dźwięku (sprawdź, które zmienne są dostępne)
                    if (typeof destructionSound !== 'undefined' && destructionSound) {
                        playSoundEffect(destructionSound);
                    } else if (typeof laserSound !== 'undefined' && laserSound) {
                        playSoundEffect(laserSound);
                    } else {
                        // Fallback - odtwórz dźwięk bez określania bufora
                        playSoundEffect('laser');
                    }
                }

                if (typeof playParticleEffect === 'function') {
                    playParticleEffect("laserHit", plaque.mesh.position);
                }

                // Usuń laser
                if (typeof poolManager !== 'undefined' && poolManager.returnObject) {
                    laser.mesh.setEnabled(false);

                    poolManager.returnObject("laser", laser);
                } else {

                    laser.mesh.dispose();
                }
                lasers.splice(i, 1);

                // Sprawdź czy blaszka została zniszczona
                if (plaque.currentHitPoints <= 0) {
                    // Dodaj punkty
                    if (typeof score !== 'undefined') {
                        const pointsToAdd = 10 * plaque.ahaType; // Więcej punktów za trudniejsze blaszki
                        score += pointsToAdd;
                        console.log(`Zniszczono blaszkę miażdżycową typu AHA ${plaque.ahaType}. Dodano ${pointsToAdd} punktów.`);
                    }

                    // Efekty zniszczenia
                    if (typeof playParticleEffect === 'function') {
                        playParticleEffect("obstacleDestruction", plaque.mesh.position);
                    }

                    // Usuń blaszkę
                    plaque.mesh.dispose();
                    atheroscleroticPlaques.splice(j, 1);

                    console.log(`Zniszczono blaszkę miażdżycową typu AHA ${plaque.ahaType}`);
                } else {
                    // Efekt wizualny trafienia (np. zmiana koloru)
                    if (plaque.mesh.material) {
                        plaque.mesh.material.emissiveColor = new BABYLON.Color3(1, 0.3, 0.3); // Czerwony błysk

                        // Przywróć oryginalny kolor po chwili
                        setTimeout(() => {
                            if (plaque.mesh && !plaque.mesh.isDisposed() && plaque.mesh.material) {
                                const colorConfig = PLAQUE_CONFIG.COLORS[plaque.type] || {
                                    BASE: new BABYLON.Color3(0.7, 0.7, 0.7),
                                    EMISSION: new BABYLON.Color3(0.3, 0.3, 0.3),
                                    DAMAGE: 10
                                };
                                plaque.mesh.material.emissiveColor = colorConfig.EMISSION;
                            }
                        }, 200);
                    }
                }

                // Aktualizuj HUD
                if (typeof updateHUD === 'function') {
                    updateHUD();
                }

                break; // Laser trafił w coś, przejdź do następnego lasera
            }
        }
    }
}

/**
 * Przełącza widok przekroju poprzecznego zmian miażdżycowych
 * @param {boolean} enabled - Czy widok przekroju ma być włączony
 * @param {Object} vessel - Obiekt naczynia krwionośnego (opcjonalny)
 * @param {Object} innerVessel - Obiekt wewnętrznego naczynia krwionośnego (opcjonalny)
 */
function toggleCrossSectionView(enabled, vessel, innerVessel) {
    // Flaga stanu widoku przekroju
    window.crossSectionEnabled = enabled;

    // Aktualizuj przezroczystość zmian miażdżycowych
    for (let i = 0; i < atheroscleroticPlaques.length; i++) {
        const plaque = atheroscleroticPlaques[i];
        if (plaque.mesh && plaque.mesh.material) {
            if (enabled) {
                plaque.mesh.material.alpha = 0.8;
            } else {
                plaque.mesh.material.alpha = 1.0;
            }
        }
    }

    // Jeśli przekazano obiekty naczyń, zaktualizuj ich przezroczystość
    if (vessel && vessel.material) {
        vessel.material.alpha = enabled ? 0.1 : 0.3;
    }

    if (innerVessel && innerVessel.material) {
        innerVessel.material.alpha = enabled ? 0.05 : 0.2;
    }

    console.log(`Widok przekroju ${enabled ? 'włączony' : 'wyłączony'}`);
}

// Ulepszony detektor kolizji dla zmian miażdżycowych
function improvedPlaqueCollisionDetection(playerMesh, plaque) {
    if (!playerMesh) {
        return false;
    }

    // Dodatkowe sprawdzenie, czy playerMesh jest w ogóle meshem, który ma bounding box
    if (!playerMesh.getBoundingInfo || !playerMesh.getBoundingInfo().boundingSphere) {
        return false;
    }

    // Sprawdź, czy plaque.mesh istnieje
    if (!plaque.mesh) {
        return false;
    }

    // Sprawdź, czy plaque.mesh ma dzieci (jest TransformNode)
    if (plaque.mesh.getChildren) {
        const children = plaque.mesh.getChildren();

        // Jeśli nie ma dzieci, ale jest TransformNode, nie możemy sprawdzić kolizji
        if (children.length === 0) {
            return false;
        }

        // Sprawdź kolizje z każdym dzieckiem
        for (let child of children) {
            // Sprawdź, czy dziecko jest meshem i jest aktywne oraz nieusunięte
            if (child instanceof BABYLON.Mesh && child.isEnabled() && !child.isDisposed()) {
                // Użyj intersectsMesh z `true` dla precyzyjnej kolizji z geometrią dziecka
                // Sprawdź również, czy child ma bounding info, zanim go użyjesz
                if (child.getBoundingInfo && playerMesh.intersectsMesh(child, true)) {
                    return true;
                }
            }
        }
    } else if (plaque.mesh instanceof BABYLON.Mesh) {
        // Jeśli plaque.mesh jest bezpośrednio meshem, sprawdź kolizję z nim
        if (plaque.mesh.getBoundingInfo && playerMesh.intersectsMesh(plaque.mesh, true)) {
            return true;
        }
    }

    return false;
}

// ====== NOWE FUNKCJE POMOCNICZE 3D ======

/**
 * Generuje pozycje dla sferycznego klastra zmian miażdżycowych
 * @param {Object} section - Sekcja tunelu
 * @param {number} count - Liczba pozycji do wygenerowania
 * @returns {BABYLON.Vector3[]} Tablica pozycji 3D
 */
function generateSphericalCluster(section, count) {
    const positions = [];

    // Promień sfery klastra (30-60% promienia tunelu)
    const clusterRadius = section.radius * (0.3 + Math.random() * 0.3);

    // Centrum klastra - losowa pozycja w tunelu z pełnym 3D offsetem
    const centerOffset = generateRandom3DOffset(section.radius * 0.4);
    const clusterCenter = section.centerPoint.add(centerOffset);

    // Generuj pozycje używając algorytmu Fibonacci Sphere
    const goldenRatio = (1 + Math.sqrt(5)) / 2;

    for (let i = 0; i < count; i++) {
        // Algorytm Fibonacci Sphere dla równomiernego rozmieszczenia
        const y = 1 - (i / Math.max(1, count - 1)) * 2; // y od 1 do -1
        const radiusAtY = Math.sqrt(1 - y * y);

        const theta = 2 * Math.PI * i / goldenRatio;

        const x = Math.cos(theta) * radiusAtY;
        const z = Math.sin(theta) * radiusAtY;

        // Dodaj losową wariację promienia (70-100% nominalnego promienia)
        const radiusVariation = 0.7 + Math.random() * 0.3;
        const actualRadius = clusterRadius * radiusVariation;

        // Oblicz finalną pozycję
        const position = new BABYLON.Vector3(
            clusterCenter.x + x * actualRadius,
            clusterCenter.y + y * actualRadius,
            clusterCenter.z + z * actualRadius
        );

        // Upewnij się, że pozycja jest w tunelu
        positions.push(constrainPositionToTunnelSection(position, section));
    }

    return positions;
}

/**
 * Generuje pozycje dla elipsoidalnego klastra zmian miażdżycowych
 * @param {Object} section - Sekcja tunelu
 * @param {number} count - Liczba pozycji do wygenerowania
 * @returns {BABYLON.Vector3[]} Tablica pozycji 3D
 */
function generateEllipsoidalCluster(section, count) {
    const positions = [];

    // Losowe wymiary elipsoidy (różne dla każdej osi)
    const baseRadius = section.radius * 0.4;
    const radiusX = baseRadius * (0.5 + Math.random() * 0.8); // 0.5-1.3 względem base
    const radiusY = baseRadius * (0.5 + Math.random() * 0.8);
    const radiusZ = baseRadius * (0.5 + Math.random() * 0.8);

    // Centrum elipsoidy
    const centerOffset = generateRandom3DOffset(section.radius * 0.3);
    const ellipsoidCenter = section.centerPoint.add(centerOffset);

    // Losowa orientacja elipsoidy
    const rotationX = Math.random() * Math.PI * 2;
    const rotationY = Math.random() * Math.PI * 2;
    const rotationZ = Math.random() * Math.PI * 2;

    // Macierz rotacji
    const rotMatrix = BABYLON.Matrix.RotationYawPitchRoll(rotationY, rotationX, rotationZ);

    for (let i = 0; i < count; i++) {
        // Generuj punkt na jednostkowej sferze
        const phi = Math.random() * Math.PI * 2;
        const theta = Math.acos(2 * Math.random() - 1); // Równomierny rozkład na sferze

        let x = Math.sin(theta) * Math.cos(phi);
        let y = Math.sin(theta) * Math.sin(phi);
        let z = Math.cos(theta);

        // Skaluj do wymiarów elipsoidy
        x *= radiusX;
        y *= radiusY;
        z *= radiusZ;

        // Zastosuj rotację
        const localPoint = new BABYLON.Vector3(x, y, z);
        const rotatedPoint = BABYLON.Vector3.TransformCoordinates(localPoint, rotMatrix);

        // Dodaj do centrum elipsoidy
        const position = ellipsoidCenter.add(rotatedPoint);

        positions.push(constrainPositionToTunnelSection(position, section));
    }

    return positions;
}

/**
 * Generuje pozycje dla warstwowego/spiralnego klastra zmian miażdżycowych
 * @param {Object} section - Sekcja tunelu
 * @param {number} count - Liczba pozycji do wygenerowania
 * @returns {BABYLON.Vector3[]} Tablica pozycji 3D
 */
function generateLayeredCluster(section, count) {
    const positions = [];

    // Parametry spirali/warstwy
    const layers = Math.min(5, Math.max(2, Math.floor(count / 3))); // 2-5 warstw
    const itemsPerLayer = Math.ceil(count / layers);
    const baseRadius = section.radius * 0.35;
    const layerHeight = section.radius * 0.3; // Wysokość całej struktury warstwowej

    // Centrum struktury
    const centerOffset = generateRandom3DOffset(section.radius * 0.25);
    const structureCenter = section.centerPoint.add(centerOffset);

    // Losowa orientacja osi spirali
    const spiralAxis = generateRandomUnitVector();
    const spiralUp = generateRandomUnitVector();
    const spiralRight = BABYLON.Vector3.Cross(spiralAxis, spiralUp).normalize();
    spiralUp.copyFrom(BABYLON.Vector3.Cross(spiralRight, spiralAxis).normalize());

    let itemsPlaced = 0;

    for (let layer = 0; layer < layers && itemsPlaced < count; layer++) {
        const layerProgress = layer / Math.max(1, layers - 1); // 0 do 1
        const layerRadius = baseRadius * (0.6 + layerProgress * 0.7); // Wzrastający promień
        const layerZ = (layerProgress - 0.5) * layerHeight; // Od -height/2 do +height/2

        const itemsInThisLayer = Math.min(itemsPerLayer, count - itemsPlaced);

        for (let item = 0; item < itemsInThisLayer; item++) {
            // Kąt z efektem spirali
            const spiralFactor = 0.5 + Math.random() * 1.0; // Różna intensywność spirali
            const angle = (item / itemsInThisLayer) * Math.PI * 2 + layer * spiralFactor;

            // Pozycja w lokalnym układzie współrzędnych spirali
            const localX = Math.cos(angle) * layerRadius;
            const localY = Math.sin(angle) * layerRadius;
            const localZ = layerZ;

            // Dodaj losową wariację
            const variance = 0.2;
            const varX = localX + (Math.random() - 0.5) * layerRadius * variance;
            const varY = localY + (Math.random() - 0.5) * layerRadius * variance;
            const varZ = localZ + (Math.random() - 0.5) * layerHeight * variance;

            // Przekształć do globalnego układu współrzędnych
            const localPos = new BABYLON.Vector3(varX, varY, varZ);
            const globalOffset = spiralRight.scale(localPos.x)
                .add(spiralUp.scale(localPos.y))
                .add(spiralAxis.scale(localPos.z));

            const position = structureCenter.add(globalOffset);

            positions.push(constrainPositionToTunnelSection(position, section));
            itemsPlaced++;
        }
    }

    return positions;
}

/**
 * Generuje losowy 3D offset w sferycznej przestrzeni
 * @param {number} maxRadius - Maksymalny promień offsetu
 * @returns {BABYLON.Vector3} Losowy offset 3D
 */
function generateRandom3DOffset(maxRadius) {
    // Równomierny rozkład w sferze
    const phi = Math.random() * Math.PI * 2;
    const theta = Math.acos(2 * Math.random() - 1);
    const radius = Math.cbrt(Math.random()) * maxRadius; // Cbrt zapewnia równomierny rozkład w objętości

    return new BABYLON.Vector3(
        radius * Math.sin(theta) * Math.cos(phi),
        radius * Math.sin(theta) * Math.sin(phi),
        radius * Math.cos(theta)
    );
}

/**
 * Generuje losowy wektor jednostkowy
 * @returns {BABYLON.Vector3} Znormalizowany wektor
 */
function generateRandomUnitVector() {
    const phi = Math.random() * Math.PI * 2;
    const theta = Math.acos(2 * Math.random() - 1);

    return new BABYLON.Vector3(
        Math.sin(theta) * Math.cos(phi),
        Math.sin(theta) * Math.sin(phi),
        Math.cos(theta)
    );
}

/**
 * Ogranicza pozycję do wnętrza sekcji tunelu
 * @param {BABYLON.Vector3} position - Pozycja do ograniczenia
 * @param {Object} section - Sekcja tunelu
 * @returns {BABYLON.Vector3} Ograniczona pozycja
 */
function constrainPositionToTunnelSection(position, section) {
    // Oblicz wektor od centrum sekcji do pozycji
    const offset = position.subtract(section.centerPoint);

    // Usuń składową wzdłuż osi tunelu
    const axialComponent = BABYLON.Vector3.Dot(offset, section.direction);
    const axialVector = section.direction.scale(axialComponent);
    const radialOffset = offset.subtract(axialVector);

    // Sprawdź odległość od osi tunelu
    const distanceFromAxis = radialOffset.length();
    const maxDistance = section.radius * 0.85; // 85% promienia tunelu

    if (distanceFromAxis > maxDistance) {
        // Skrać radialny offset
        const scaledRadialOffset = radialOffset.normalize().scale(maxDistance);
        return section.centerPoint.add(axialVector).add(scaledRadialOffset);
    }

    return position;
}

/**
 * Oblicza pozycję klastra w pełnej 3D przestrzeni tunelu
 * @param {BABYLON.Vector3} basePosition - Bazowa pozycja
 * @param {number} size - Rozmiar klastra
 * @returns {BABYLON.Vector3} Skorygowana pozycja 3D
 */
function calculate3DPositionInTunnel(basePosition, size) {
    // Dodaj pełny 3D offset zamiast tylko w płaszczyźnie XY
    const offsetRadius = size * PLAQUE_CONFIG.CLUSTER_3D.POSITION_OFFSET_RATIO;

    // Pełne 3D przesunięcie używając współrzędnych sferycznych
    const phi = Math.random() * Math.PI * 2; // Azymut (0-2π)
    const theta = Math.random() * Math.PI; // Elewacja (0-π)
    const r = Math.random() * offsetRadius; // Promień (0-offsetRadius)

    const offsetX = r * Math.sin(theta) * Math.cos(phi);
    const offsetY = r * Math.sin(theta) * Math.sin(phi);
    const offsetZ = r * Math.cos(theta);

    return new BABYLON.Vector3(
        basePosition.x + offsetX,
        basePosition.y + offsetY,
        basePosition.z + offsetZ
    );
}

/**
 * Tworzy materiał 3D z zaawansowanymi efektami
 * @param {string} type - Typ materiału
 * @param {Object} ahaTypeInfo - Informacje o typie AHA
 * @param {BABYLON.Scene} scene - Scena
 * @param {number} index - Indeks sfery
 * @returns {BABYLON.StandardMaterial} Materiał 3D
 */
function create3DMaterial(type, ahaTypeInfo, scene, index) {
    const mat = new BABYLON.StandardMaterial(`plaqueMat_${type}_${atheroscleroticPlaques.length}_${index}`, scene);

    // Podstawowe ustawienia materiału
    if (ahaTypeInfo && ahaTypeInfo.texture) {
        mat.diffuseTexture = new BABYLON.Texture(ahaTypeInfo.texture, scene);
    } else {
        const colorConfig = PLAQUE_CONFIG.COLORS[type] || {
            BASE: new BABYLON.Color3(0.7, 0.7, 0.7),
            EMISSION: new BABYLON.Color3(0.3, 0.3, 0.3),
            DAMAGE: 10
        };
        mat.diffuseColor = colorConfig.BASE;
        mat.emissiveColor = colorConfig.EMISSION;
    }

    // Zaawansowane efekty 3D
    mat.specularColor = new BABYLON.Color3(0.7, 0.7, 0.7);
    mat.specularPower = 32;

    // Normal mapping
    try {
        mat.bumpTexture = new BABYLON.Texture("textures/others_0003_normal_directx_1k.png", scene);
        mat.bumpTexture.level = 1.2;
    } catch (e) {
        console.warn("Nie można załadować normal texture");
    }

    // Ambient Occlusion
    try {
        mat.ambientTexture = new BABYLON.Texture("textures/others_0003_ao_1k.jpg", scene);
        mat.ambientColor = new BABYLON.Color3(1, 1, 1);
    } catch (e) {
        console.warn("Nie można załadować ambient occlusion texture");
    }

    // Fresnel effect dla lepszego efektu 3D
    mat.useReflectionFresnelFromSpecular = true;
    mat.reflectionFresnelParameters = new BABYLON.FresnelParameters();
    mat.reflectionFresnelParameters.bias = 0.02;
    mat.reflectionFresnelParameters.power = 2.5;
    mat.reflectionFresnelParameters.leftColor = BABYLON.Color3.White();
    mat.reflectionFresnelParameters.rightColor = BABYLON.Color3.Black();

    // Właściwości 3D
    mat.backFaceCulling = false;
    mat.twoSidedLighting = true;

    return mat;
}

/**
 * Stosuje organiczną deformację do meshu
 * @param {BABYLON.Mesh} mesh - Mesh do zdeformowania
 * @param {number} intensity - Intensywność deformacji (0-1)
 */
function applyOrganicDeformation(mesh, intensity = 0.3) {
    const positions = mesh.getVerticesData(BABYLON.VertexBuffer.PositionKind);
    if (!positions) return;

    const deformedPositions = [];
    const vertexCount = positions.length / 3;

    // Oblicz środek geometryczny
    let centerX = 0, centerY = 0, centerZ = 0;
    for (let j = 0; j < positions.length; j += 3) {
        centerX += positions[j];
        centerY += positions[j + 1];
        centerZ += positions[j + 2];
    }
    centerX /= vertexCount;
    centerY /= vertexCount;
    centerZ /= vertexCount;

    // Zastosuj 3D deformację
    for (let j = 0; j < positions.length; j += 3) {
        const vx = positions[j] - centerX;
        const vy = positions[j + 1] - centerY;
        const vz = positions[j + 2] - centerZ;

        const dist = Math.sqrt(vx*vx + vy*vy + vz*vz);

        // Pełna 3D deformacja
        const noiseX = 1 + (Math.random() - 0.5) * intensity;
        const noiseY = 1 + (Math.random() - 0.5) * intensity;
        const noiseZ = 1 + (Math.random() - 0.5) * intensity;

        const distFactor = 1 + (dist * 0.2);

        deformedPositions.push(positions[j] * noiseX * distFactor);
        deformedPositions.push(positions[j + 1] * noiseY * distFactor);
        deformedPositions.push(positions[j + 2] * noiseZ * distFactor);
    }

    mesh.updateVerticesData(BABYLON.VertexBuffer.PositionKind, deformedPositions);
    mesh.createNormals(true);
}

/**
 * Zunifikowana funkcja generowania zmian miażdżycowych
 * Łączy funkcjonalność oryginalnej i zunifikowanej wersji
 * @param {Array} tunnelSections - Sekcje tunelu
 * @param {number} gameLevel - Aktualny poziom gry
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function generateAtheroscleroticPlaques(tunnelSections, gameLevel, scene) {
    if (!tunnelSections || tunnelSections.length === 0 || !scene) {
        console.error("Nie można wygenerować zmian miażdżycowych - brak sekcji tunelu lub sceny");
        return;
    }

    // Wyczyść istniejące zmiany miażdżycowe
    clearAtheroscleroticPlaques();

    // Określ liczbę klastrów na podstawie poziomu trudności
    let clustersCount;
    if (gameLevel <= 6) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.EASY;
    } else if (gameLevel <= 12) {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.MEDIUM;
    } else {
        clustersCount = PLAQUE_CONFIG.CLUSTERS_PER_LEVEL.HARD;
    }

    console.log(`Generowanie ${clustersCount} klastrów zmian miażdżycowych dla poziomu ${gameLevel}`);

    // Wybierz losowe sekcje tunelu dla klastrów (z wyłączeniem pierwszych i ostatnich 10%)
    const validSectionIndices = [];
    const startExcludeCount = Math.floor(tunnelSections.length * 0.1);
    const endExcludeCount = Math.floor(tunnelSections.length * 0.1);

    for (let i = startExcludeCount; i < tunnelSections.length - endExcludeCount; i++) {
        validSectionIndices.push(i);
    }

    // Wymieszaj indeksy, aby uzyskać losowe rozmieszczenie
    shuffleArray(validSectionIndices);

    // Wybierz sekcje dla klastrów
    const clusterSectionIndices = validSectionIndices.slice(0, clustersCount);

    // Sprawdź czy używamy zunifikowanego systemu
    const useUnifiedSystem = typeof window.unifiedObjectManager !== 'undefined';

    if (useUnifiedSystem) {
        // Generuj klastry z throttling'iem dla wydajności
        let clustersGenerated = 0;

        function generateNextCluster() {
            if (clustersGenerated >= clusterSectionIndices.length) {
                console.log(`Wygenerowano ${atheroscleroticPlaques.length} zmian miażdżycowych w ${clustersCount} klastrach`);
                return;
            }

            // Sprawdź throttling
            if (window.unifiedObjectManager.canSpawn('plaqueCluster')) {
                const sectionIndex = clusterSectionIndices[clustersGenerated];
                const section = tunnelSections[sectionIndex];

                const plaquesInCluster = Math.floor(
                    PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN +
                    Math.random() * (PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MAX - PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN + 1)
                );

                generatePlaqueCluster(section, plaquesInCluster, scene);
                clustersGenerated++;
            }

            // Schedułuj następny klaster
            setTimeout(generateNextCluster, 50); // 50ms delay dla wydajności
        }

        generateNextCluster();
    } else {
        // Generuj klastry zmian miażdżycowych (oryginalna metoda)
        for (let i = 0; i < clusterSectionIndices.length; i++) {
            const sectionIndex = clusterSectionIndices[i];
            const section = tunnelSections[sectionIndex];

            // Określ liczbę zmian w klastrze
            const plaquesInCluster = Math.floor(
                PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN +
                Math.random() * (PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MAX - PLAQUE_CONFIG.PLAQUES_PER_CLUSTER.MIN + 1)
            );

            // Generuj zmiany w klastrze
            generatePlaqueCluster(section, plaquesInCluster, scene);
        }

        console.log(`Wygenerowano ${atheroscleroticPlaques.length} zmian miażdżycowych w ${clustersCount} klastrach`);
    }
}

/**
 * Zunifikowana funkcja generująca klaster zmian miażdżycowych w pełnej 3D przestrzeni tunelu
 * Łączy funkcjonalność oryginalnej i zunifikowanej wersji
 * @param {Object} section - Sekcja tunelu
 * @param {number} plaquesCount - Liczba zmian w klastrze
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function generatePlaqueCluster(section, plaquesCount, scene) {
    if (!section || !section.centerPoint || !section.radius) {
        console.warn("Nieprawidłowa sekcja tunelu dla klastra zmian miażdżycowych");
        return;
    }

    // 1. Wybierz typ rozmieszczenia klastra
    const clusterType = Math.random();
    let plaquePositions = [];

    if (clusterType < 0.4) {
        // Typ 1: Rozmieszczenie sferyczne (40% szans)
        plaquePositions = generateSphericalCluster(section, plaquesCount);
    } else if (clusterType < 0.7) {
        // Typ 2: Rozmieszczenie elipsoidalne (30% szans)
        plaquePositions = generateEllipsoidalCluster(section, plaquesCount);
    } else {
        // Typ 3: Rozmieszczenie warstwowe/spiralne (30% szans)
        plaquePositions = generateLayeredCluster(section, plaquesCount);
    }

    // Fallback jeśli funkcje 3D nie są dostępne
    if (plaquePositions.length === 0) {
        console.warn("Funkcje 3D nie są dostępne, używam zunifikowanego systemu pozycjonowania");

        // Sprawdź czy używamy zunifikowanego systemu
        const useUnifiedSystem = typeof window.unifiedObjectManager !== 'undefined';

        for (let i = 0; i < plaquesCount; i++) {
            const baseDiameter = PLAQUE_CONFIG.SIZE.MIN + 
                Math.random() * (PLAQUE_CONFIG.SIZE.MAX - PLAQUE_CONFIG.SIZE.MIN);

            const tunnelSizeFactor = Math.min(1.2, section.radius / 2.0);
            const plaqueDiameter = baseDiameter * tunnelSizeFactor;

            let position;

            if (useUnifiedSystem) {
                // Znajdź pozycję z unified system
                position = window.unifiedObjectManager.findSafePosition(section, plaqueDiameter);
            } else {
                // Fallback do prostego pozycjonowania
                const angle = Math.random() * Math.PI * 2;
                const radius = section.radius * 0.7;
                position = new BABYLON.Vector3(
                    section.centerPoint.x + Math.cos(angle) * radius,
                    section.centerPoint.y + Math.sin(angle) * radius,
                    section.centerPoint.z
                );
            }

            if (position) {
                const plaqueType = getRandomPlaqueType();
                createPlaque(position, plaqueDiameter, plaqueType, scene);
            }
        }
        return;
    }

    // 2. Utwórz zmiany miażdżycowe na wygenerowanych pozycjach
    for (let i = 0; i < plaquePositions.length; i++) {
        const position = plaquePositions[i];

        // Losowy rozmiar zmiany z lepszą kontrolą proporcji
        const baseDiameter = PLAQUE_CONFIG.SIZE.MIN +
            Math.random() * (PLAQUE_CONFIG.SIZE.MAX - PLAQUE_CONFIG.SIZE.MIN);

        // Dostosuj rozmiar do wielkości tunelu dla lepszych proporcji
        const tunnelSizeFactor = Math.min(1.2, section.radius / 2.0);
        const plaqueDiameter = baseDiameter * tunnelSizeFactor;

        // Losowy typ zmiany miażdżycowej
        const plaqueType = getRandomPlaqueType();

        // Utwórz zmianę miażdżycową
        createPlaque(position, plaqueDiameter, plaqueType, scene);
    }
}

/**
 * Zunifikowana funkcja tworząca pojedynczą zmianę miażdżycową z gwarantowaną 3D strukturą
 * Łączy funkcjonalność oryginalnej i zunifikowanej wersji
 * @param {BABYLON.Vector3} position - Pozycja zmiany
 * @param {number} size - Rozmiar zmiany
 * @param {string} type - Typ zmiany (CALCIFIED, LIPID, FIBROUS)
 * @param {BABYLON.Scene} scene - Scena Babylon.js
 */
function createPlaque(position, size, type, scene) {
    // Określ typ AHA na podstawie typu zmiany
    let ahaType;
    if (type === 'CALCIFIED') {
        ahaType = Math.random() < 0.7 ? 5 : 6;
    } else if (type === 'LIPID') {
        ahaType = Math.random() < 0.6 ? 2 : (Math.random() < 0.5 ? 1 : 3);
    } else {
        ahaType = Math.random() < 0.6 ? 3 : (Math.random() < 0.5 ? 4 : 5);
    }
    const ahaTypeInfo = PLAQUE_TYPES.find(t => t.type === ahaType);

    // --- KLASTER 3D: Zapewnienie pełnej trójwymiarowości ---
    const cluster = new BABYLON.TransformNode(`plaqueCluster_${type}_${atheroscleroticPlaques.length}`, scene);

    // Zwiększona liczba sfer dla bardziej złożonego klastra
    const spheresCount = 12 + Math.floor(Math.random() * 8); // 12-19 sfer

    // Właściwy promień klastra - zapewniamy że wszystkie wymiary są równorzędne
    const maxClusterRadius = size / 2;

    // KLUCZOWA ZMIANA: Używamy pełnej sfery do rozmieszczenia sfer
    // zamiast koncentrowania się na jednej płaszczyźnie
    const distributionSphereRadius = maxClusterRadius * PLAQUE_CONFIG.CLUSTER_3D.SPHERE_DISTRIBUTION_RADIUS;
    const maxIndividualSphereRadius = maxClusterRadius * PLAQUE_CONFIG.CLUSTER_3D.MAX_INDIVIDUAL_SPHERE_RATIO;

    // Generuj punkty na sferze używając algorytmu Fibonacci Sphere dla równomiernego rozmieszczenia
    const goldenRatio = (1 + Math.sqrt(5)) / 2;
    const spherePositions = [];

    for (let i = 0; i < spheresCount; i++) {
        // Algorytm Fibonacci Sphere zapewnia równomierne rozmieszczenie na sferze
        const y = 1 - (i / (spheresCount - 1)) * 2; // y od 1 do -1
        const radiusAtY = Math.sqrt(1 - y * y);

        const theta = 2 * Math.PI * i / goldenRatio;

        const x = Math.cos(theta) * radiusAtY;
        const z = Math.sin(theta) * radiusAtY;

        // Dodaj losowe przesunięcie dla mniej regularnego układu
        const randomOffset = 0.3; // 30% losowego przesunięcia
        const offsetX = x + (Math.random() - 0.5) * randomOffset;
        const offsetY = y + (Math.random() - 0.5) * randomOffset;
        const offsetZ = z + (Math.random() - 0.5) * randomOffset;

        // Normalizuj i przeskaluj do promienia rozmieszczenia
        const length = Math.sqrt(offsetX * offsetX + offsetY * offsetY + offsetZ * offsetZ);
        const normalizedX = (offsetX / length) * distributionSphereRadius;
        const normalizedY = (offsetY / length) * distributionSphereRadius;
        const normalizedZ = (offsetZ / length) * distributionSphereRadius;

        spherePositions.push({
            x: normalizedX,
            y: normalizedY,
            z: normalizedZ
        });
    }

    for (let i = 0; i < spheresCount; i++) {
        const spherePos = spherePositions[i];

        // Rozmiar indywidualnej sfery z większą kontrolą
        const individualSphereDiameter = maxIndividualSphereRadius * (1.2 + Math.random() * 0.8); // 1.2-2.0 względem base

        // Tworzenie różnorodnych 3D kształtów
        let sphere;
        const shapeType = Math.random();

        if (shapeType < 0.2) {
            // Dodecahedron
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaquePolyhedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 2, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.35) {
            // Icosahedron
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaqueIcosahedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 1, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.5) {
            // Octahedron
            sphere = BABYLON.MeshBuilder.CreatePolyhedron(
                `plaqueOctahedron_${type}_${atheroscleroticPlaques.length}_${i}`,
                { type: 0, size: individualSphereDiameter / 2 },
                scene
            );
        } else if (shapeType < 0.8) {
            // Deformowana sfera
            sphere = BABYLON.MeshBuilder.CreateSphere(
                `plaqueSphere_${type}_${atheroscleroticPlaques.length}_${i}`,
                { diameter: individualSphereDiameter, segments: 24 },
                scene
            );

            // Zaawansowana deformacja dla organicznego wyglądu
            applyOrganicDeformation(sphere, PLAQUE_CONFIG.CLUSTER_3D.DEFORMATION_INTENSITY);
        } else {
            // Elipsoida z pełną 3D deformacją
            sphere = BABYLON.MeshBuilder.CreateSphere(
                `plaqueEllipsoid_${type}_${atheroscleroticPlaques.length}_${i}`,
                { diameter: individualSphereDiameter, segments: 20 },
                scene
            );

            // KLUCZOWA ZMIANA: Równomierne skalowanie we wszystkich 3 wymiarach
            const baseScale = PLAQUE_CONFIG.CLUSTER_3D.UNIFORM_SCALE_BASE + Math.random() * 0.6; // 0.85-1.45 bazowa skala
            const scaleVariation = PLAQUE_CONFIG.CLUSTER_3D.SCALE_AXIS_VARIATION; // Maksymalna wariacja między osiami

            const scaleX = baseScale + (Math.random() - 0.5) * scaleVariation;
            const scaleY = baseScale + (Math.random() - 0.5) * scaleVariation;
            const scaleZ = baseScale + (Math.random() - 0.5) * scaleVariation;

            sphere.scaling = new BABYLON.Vector3(scaleX, scaleY, scaleZ);
        }

        sphere.parent = cluster;
        sphere.position = new BABYLON.Vector3(spherePos.x, spherePos.y, spherePos.z);

        // Dodaj pełną 3D rotację każdej sfery
        sphere.rotation.x = Math.random() * Math.PI * 2;
        sphere.rotation.y = Math.random() * Math.PI * 2;
        sphere.rotation.z = Math.random() * Math.PI * 2;

        // Materiał z zaawansowanymi efektami 3D
        const mat = create3DMaterial(type, ahaTypeInfo, scene, i);
        sphere.material = mat;

        // Właściwości 3D
        sphere.receiveShadows = true;
        sphere.checkCollisions = true;
        sphere.isPickable = true;
    }

    // KLUCZOWA ZMIANA: Pozycjonowanie klastra w pełnej 3D przestrzeni tunelu
    cluster.position = calculate3DPositionInTunnel(position, size);

    // Pełna 3D rotacja klastra dla maksymalnej różnorodności
    cluster.rotation = new BABYLON.Vector3(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
    );

    // KLUCZOWA ZMIANA: Równomierne skalowanie we wszystkich wymiarach
    const uniformScale = PLAQUE_CONFIG.CLUSTER_3D.UNIFORM_SCALE_BASE + Math.random() * PLAQUE_CONFIG.CLUSTER_3D.UNIFORM_SCALE_VARIATION;
    const scaleVariation = PLAQUE_CONFIG.CLUSTER_3D.SCALE_AXIS_VARIATION; // Maksymalna wariacja między osiami

    const clusterScaleX = uniformScale + (Math.random() - 0.5) * scaleVariation;
    const clusterScaleY = uniformScale + (Math.random() - 0.5) * scaleVariation;
    const clusterScaleZ = uniformScale + (Math.random() - 0.5) * scaleVariation;

    cluster.scaling = new BABYLON.Vector3(clusterScaleX, clusterScaleY, clusterScaleZ);

    // Zapisz info o klastrze
    const plaqueData = {
        mesh: cluster,
        type: type,
        ahaType: ahaType,
        damage: (ahaTypeInfo && ahaTypeInfo.damage) ? ahaTypeInfo.damage : 10,
        size: size,
        originalPosition: cluster.position.clone(),
        destructible: ahaTypeInfo ? ahaTypeInfo.destructible : false,
        hitPoints: ahaTypeInfo && ahaTypeInfo.hitPoints ? ahaTypeInfo.hitPoints : 0,
        currentHitPoints: ahaTypeInfo && ahaTypeInfo.hitPoints ? ahaTypeInfo.hitPoints : 0
    };

    atheroscleroticPlaques.push(plaqueData);

    // Inicjalizuj parametry animacji
    plaqueData.animParams = {
        rotationSpeed: 0.0005 + Math.random() * 0.001,
        pulseFreq: 0.0005 + Math.random() * 0.001,
        pulseAmp: 0.03 + Math.random() * 0.04,
        wobbleFreq: 0.0007 + Math.random() * 0.0006,
        wobbleAmp: 0.03 + Math.random() * 0.03,
        phaseOffset: Math.random() * Math.PI * 2,
        originalScale: cluster.scaling.clone()
    };

    // Upewnij się, że animacja jest włączona
    window.animationEnabled = true;

    // Zarejestruj w unified system jeśli dostępny
    if (typeof window.unifiedObjectManager !== 'undefined') {
        window.unifiedObjectManager.registerObject(
            'atheroscleroticPlaques', 
            position, 
            size, 
            cluster
        );
    }
}

// Eksportuj funkcje do globalnego obiektu window
window.PLAQUE_TYPES = PLAQUE_TYPES;
window.generateAtheroscleroticPlaques = generateAtheroscleroticPlaques;
window.updateAtheroscleroticPlaques = updateAtheroscleroticPlaques;
window.checkPlaqueCollisions = checkPlaqueCollisions;
window.clearAtheroscleroticPlaques = clearAtheroscleroticPlaques;
window.checkLaserPlaqueCollisions = checkLaserPlaqueCollisions;
window.toggleCrossSectionView = toggleCrossSectionView;
window.toggleAnimation = toggleAnimation;

// Nowe funkcje 3D
window.generateSphericalCluster = generateSphericalCluster;
window.generateEllipsoidalCluster = generateEllipsoidalCluster;
window.generateLayeredCluster = generateLayeredCluster;
window.generateRandom3DOffset = generateRandom3DOffset;
window.generateRandomUnitVector = generateRandomUnitVector;
window.constrainPositionToTunnelSection = constrainPositionToTunnelSection;
window.calculate3DPositionInTunnel = calculate3DPositionInTunnel;
window.create3DMaterial = create3DMaterial;
window.applyOrganicDeformation = applyOrganicDeformation;

// Dla kompatybilności z unifiedObjectSystem.js
window.generateAtheroscleroticPlaquesUnified = generateAtheroscleroticPlaques;

// Dummy funkcja createHoneycombTexture (nieużywana, ale eksportowana dla kompatybilności)
function createHoneycombTexture() {
    // Funkcja nie jest już używana, zwraca null
    return null;
}
window.createHoneycombTexture = createHoneycombTexture;

console.log("✅ Załadowano zintegrowany system zmian miażdżycowych z pełnym wsparciem 3D");
